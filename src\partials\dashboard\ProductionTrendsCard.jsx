import React, { useState, useEffect } from 'react';

function ProductionTrendsCard() {
  const [selectedMetric, setSelectedMetric] = useState('production');
  const [isAnimating, setIsAnimating] = useState(false);

  const metrics = {
    production: {
      title: 'Production Volume',
      value: '2,847L',
      change: '+12.5%',
      trend: 'up',
      data: [65, 70, 68, 75, 80, 85, 90]
    },
    quality: {
      title: 'Quality Score',
      value: '94.8%',
      change: '+1.1%',
      trend: 'up',
      data: [92, 93, 91, 94, 95, 94, 95]
    },
    efficiency: {
      title: 'Efficiency Rate',
      value: '87.3%',
      change: '+3.2%',
      trend: 'up',
      data: [82, 84, 83, 86, 88, 87, 89]
    }
  };

  const currentMetric = metrics[selectedMetric];

  useEffect(() => {
    setIsAnimating(true);
    const timer = setTimeout(() => setIsAnimating(false), 500);
    return () => clearTimeout(timer);
  }, [selectedMetric]);

  const handleMetricChange = (metric) => {
    setSelectedMetric(metric);
  };

  return (
    <div className="card glow-on-hover">
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-2">
            Production Trends
          </h3>
          <p className="text-sm text-slate-600 dark:text-slate-400">
            Real-time metrics and performance indicators
          </p>
        </div>

        {/* Metric Selector */}
        <div className="flex space-x-2 mb-6">
          {Object.keys(metrics).map((metric) => (
            <button
              key={metric}
              onClick={() => handleMetricChange(metric)}
              className={`px-3 py-2 text-xs font-medium rounded-lg transition-all duration-200 ${
                selectedMetric === metric
                  ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg'
                  : 'bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-600'
              }`}
            >
              {metric.charAt(0).toUpperCase() + metric.slice(1)}
            </button>
          ))}
        </div>

        {/* Current Metric Display */}
        <div className={`transition-all duration-500 ${isAnimating ? 'opacity-50 transform scale-95' : 'opacity-100 transform scale-100'}`}>
          <div className="text-center mb-6">
            <div className="text-3xl font-bold text-slate-800 dark:text-slate-100 mb-1">
              {currentMetric.value}
            </div>
            <div className="text-sm text-slate-600 dark:text-slate-400 mb-2">
              {currentMetric.title}
            </div>
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              currentMetric.trend === 'up' 
                ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-200'
                : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200'
            }`}>
              <svg className={`w-3 h-3 mr-1 ${currentMetric.trend === 'up' ? 'rotate-0' : 'rotate-180'}`} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L10 4.414 6.707 7.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              {currentMetric.change}
            </div>
          </div>

          {/* Mini Chart */}
          <div className="mb-6">
            <div className="flex items-end justify-between h-24 space-x-1">
              {currentMetric.data.map((value, index) => (
                <div
                  key={`trend-${index}`}
                  className="bg-gradient-to-t from-indigo-500 to-purple-600 rounded-t-sm transition-all duration-700 ease-out hover:from-indigo-600 hover:to-purple-700"
                  style={{
                    height: `${(value / Math.max(...currentMetric.data)) * 100}%`,
                    width: '100%',
                    animationDelay: `${index * 0.1}s`
                  }}
                ></div>
              ))}
            </div>
            <div className="flex justify-between text-xs text-slate-500 dark:text-slate-400 mt-2">
              <span>Mon</span>
              <span>Tue</span>
              <span>Wed</span>
              <span>Thu</span>
              <span>Fri</span>
              <span>Sat</span>
              <span>Sun</span>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
            <div className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              156
            </div>
            <div className="text-xs text-slate-600 dark:text-slate-400">
              Active Farms
            </div>
          </div>
          <div className="text-center p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
            <div className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              24/7
            </div>
            <div className="text-xs text-slate-600 dark:text-slate-400">
              Monitoring
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className="mt-6">
          <button className="w-full btn btn-secondary text-sm">
            View Detailed Analytics
            <svg className="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}

export default ProductionTrendsCard;
