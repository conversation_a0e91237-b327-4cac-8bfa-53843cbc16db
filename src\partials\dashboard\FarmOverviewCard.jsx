import React, { useState, useEffect } from 'react';

function FarmOverviewCard() {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate data loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, [selectedPeriod]);

  const farmData = [
    { name: 'Green Valley Farm', production: 450, quality: 96, status: 'active', location: 'Wisconsin' },
    { name: 'Sunrise Dairy Co.', production: 380, quality: 94, status: 'active', location: 'California' },
    { name: 'Mountain View Ranch', production: 320, quality: 98, status: 'active', location: 'Vermont' },
    { name: 'Prairie Winds Farm', production: 290, quality: 92, status: 'maintenance', location: 'Iowa' },
    { name: 'Riverside Dairy', production: 410, quality: 95, status: 'active', location: 'New York' },
  ];

  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return 'badge badge-success';
      case 'maintenance':
        return 'badge badge-warning';
      case 'inactive':
        return 'badge badge-danger';
      default:
        return 'badge badge-info';
    }
  };

  if (isLoading) {
    return (
      <div className="card">
        <div className="p-6">
          <div className="animate-shimmer h-6 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={`shimmer-${i}`} className="animate-shimmer h-16 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card glow-on-hover">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              Farm Overview
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              Production and quality metrics across all farms
            </p>
          </div>
          
          {/* Period Selector */}
          <div className="flex bg-slate-100 dark:bg-slate-700 rounded-lg p-1">
            {['7d', '30d', '90d'].map((period) => (
              <button
                key={period}
                onClick={() => {
                  setSelectedPeriod(period);
                  setIsLoading(true);
                }}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-all duration-200 ${
                  selectedPeriod === period
                    ? 'bg-white dark:bg-slate-600 text-indigo-600 dark:text-indigo-400 shadow-sm'
                    : 'text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200'
                }`}
              >
                {period}
              </button>
            ))}
          </div>
        </div>

        {/* Farm List */}
        <div className="space-y-4">
          {farmData.map((farm, index) => (
            <div 
              key={farm.name}
              className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 transition-all duration-200 group animate-fadeInUp"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg group-hover:scale-110 transition-transform duration-200">
                  {farm.name.charAt(0)}
                </div>
                <div>
                  <h4 className="font-medium text-slate-800 dark:text-slate-100">
                    {farm.name}
                  </h4>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    {farm.location}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-6">
                {/* Production */}
                <div className="text-center">
                  <div className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                    {farm.production}L
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">
                    Production
                  </div>
                </div>

                {/* Quality */}
                <div className="text-center">
                  <div className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                    {farm.quality}%
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">
                    Quality
                  </div>
                </div>

                {/* Status */}
                <div className="text-center">
                  <span className={getStatusBadge(farm.status)}>
                    {farm.status}
                  </span>
                </div>

                {/* Action */}
                <button className="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 transition-colors duration-200">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="mt-6 pt-6 border-t border-slate-200 dark:border-slate-700">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-slate-800 dark:text-slate-100">
                {farmData.length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Total Farms
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-emerald-600">
                {farmData.filter(f => f.status === 'active').length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Active
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-indigo-600">
                {Math.round(farmData.reduce((acc, farm) => acc + farm.quality, 0) / farmData.length)}%
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Avg Quality
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FarmOverviewCard;
