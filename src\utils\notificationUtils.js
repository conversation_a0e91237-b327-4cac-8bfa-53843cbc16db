/**
 * Utility functions for notification management
 */

/**
 * Determines if a notification is archived based on its end date
 * @param {Object} notification - The notification object
 * @param {string} notification.endDate - The end date of the notification (ISO string)
 * @returns {boolean} - True if the notification is archived (end date is in the past)
 */
export const isNotificationArchived = (notification) => {
  if (!notification?.endDate) {
    return false;
  }

  const endDate = new Date(notification.endDate);
  const currentDate = new Date();
  
  // Set time to end of day for end date and start of day for current date for fair comparison
  endDate.setHours(23, 59, 59, 999);
  currentDate.setHours(0, 0, 0, 0);
  
  return endDate < currentDate;
};

/**
 * Gets the effective status of a notification (considering archived state)
 * @param {Object} notification - The notification object
 * @param {string} notification.status - The original status ('draft' or 'published')
 * @param {string} notification.endDate - The end date of the notification
 * @returns {string} - The effective status ('archived', 'draft', or 'published')
 */
export const getNotificationStatus = (notification) => {
  if (isNotificationArchived(notification)) {
    return 'archived';
  }
  return notification.status || 'draft';
};

/**
 * Filters notifications based on status filter
 * @param {Array} notifications - Array of notification objects
 * @param {Array} statusFilter - Array of filter values ('published', 'draft', 'archived')
 * @returns {Array} - Filtered notifications
 */
export const filterNotificationsByStatus = (notifications, statusFilter) => {
  if (!statusFilter || statusFilter.length === 0) {
    return notifications; // No filter applied
  }

  return notifications.filter(notification => {
    const effectiveStatus = getNotificationStatus(notification);
    return statusFilter.includes(effectiveStatus);
  });
};

/**
 * Gets the CSS class for status badge based on notification status
 * @param {Object} notification - The notification object
 * @returns {string} - CSS class string for the status badge
 */
export const getStatusBadgeClass = (notification) => {
  const status = getNotificationStatus(notification);
  
  switch (status) {
    case 'published':
      return 'badge badge-success';
    case 'draft':
      return 'badge badge-warning';
    case 'archived':
      return 'badge badge-secondary';
    default:
      return 'badge badge-info';
  }
};

/**
 * Gets the display text for notification status
 * @param {Object} notification - The notification object
 * @returns {string} - Display text for the status
 */
export const getStatusDisplayText = (notification) => {
  const status = getNotificationStatus(notification);
  
  switch (status) {
    case 'published':
      return 'Published';
    case 'draft':
      return 'Draft';
    case 'archived':
      return 'Archived';
    default:
      return 'Unknown';
  }
};
