import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardCard01 from '../partials/dashboard/DashboardCard01';
import DashboardCard02 from '../partials/dashboard/DashboardCard02';
import DashboardCard03 from '../partials/dashboard/DashboardCard03';
import DashboardCard04 from '../partials/dashboard/DashboardCard04';
import DashboardCard05 from '../partials/dashboard/DashboardCard05';
import DashboardCard06 from '../partials/dashboard/DashboardCard06';
import DashboardCard07 from '../partials/dashboard/DashboardCard07';
import DashboardCard08 from '../partials/dashboard/DashboardCard08';
import DashboardCard09 from '../partials/dashboard/DashboardCard09';
import DashboardCard10 from '../partials/dashboard/DashboardCard10';
import Dashboard<PERSON>ard11 from '../partials/dashboard/DashboardCard11';
import Dashboard<PERSON>ard12 from '../partials/dashboard/DashboardCard12';
import Dashboard<PERSON>ard13 from '../partials/dashboard/DashboardCard13';
import LatestVisits from '../partials/dashboard/LatestVisits.jsx';
import StatisticsCard from '../partials/dashboard/StatisticsCard.jsx';
import ToolsUsageCard from '../partials/dashboard/ToolsUsageCard.jsx';
import QuickNavigationCard from '../partials/dashboard/QuickNavigationCard.jsx';

function MainDashboard() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time for smooth animations
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400 animate-pulse">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Welcome Section */}
      <div className="mb-8 animate-fadeInUp">
        <div className="card-gradient p-6 rounded-xl border border-slate-200 dark:border-slate-700">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-slate-800 dark:text-slate-100 mb-2">
                Welcome to Dairy Entelligen Admin
              </h1>
              <p className="text-slate-600 dark:text-slate-400">
                Manage users, monitor app usage, send notifications, and track dairy operations across all farms.
              </p>
            </div>
            <div className="animate-float">
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Navigation */}
      <div className="mb-8 animate-fadeInUp" style={{animationDelay: '0.1s'}}>
        <QuickNavigationCard navigate={navigate} />
      </div>

      {/* DE App Statistics */}
      <div className="mb-8 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
        <StatisticsCard />
      </div>

      {/* Tools Usage Section */}
      <div className="mb-8 animate-fadeInUp" style={{animationDelay: '0.3s'}}>
        <ToolsUsageCard />
      </div>

      {/* Main Dashboard Cards */}
      <div className="grid grid-cols-12 gap-6">
        {/* Latest Visits */}
        <div className="col-span-12 animate-fadeInUp" style={{animationDelay: '0.4s'}}>
          <LatestVisits/>
        </div>
      </div>
    </>
  );
}

export default MainDashboard;
