import React from 'react';
import PropTypes from 'prop-types';

function QuickNavigationCard({ navigate }) {
  const navigationItems = [
    {
      title: 'Manage Users',
      description: 'View and manage user accounts, permissions, and access levels',
      icon: '👥',
      path: '/community/users-tabs',
      color: 'from-blue-500 to-indigo-600',
      hoverColor: 'hover:from-blue-600 hover:to-indigo-700'
    },
    {
      title: 'Notifications',
      description: 'Create and manage notifications for app users',
      icon: '📢',
      path: '/notifications',
      color: 'from-purple-500 to-pink-600',
      hoverColor: 'hover:from-purple-600 hover:to-pink-700'
    },
    {
      title: 'Analytics',
      description: 'View detailed analytics and usage reports',
      icon: '📊',
      path: '#',
      color: 'from-emerald-500 to-teal-600',
      hoverColor: 'hover:from-emerald-600 hover:to-teal-700'
    },
    {
      title: 'Settings',
      description: 'Configure app settings and system preferences',
      icon: '⚙️',
      path: '#',
      color: 'from-amber-500 to-orange-600',
      hoverColor: 'hover:from-amber-600 hover:to-orange-700'
    }
  ];

  const handleNavigation = (path) => {
    if (path !== '#') {
      navigate(path);
    }
  };

  return (
    <div className="card glow-on-hover">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              Quick Navigation
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              Access key admin functions quickly
            </p>
          </div>
          <div className="animate-pulse-gentle">
            <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {navigationItems.map((item, index) => (
            <button
              key={item.title}
              type="button"
              onClick={() => handleNavigation(item.path)}
              className={`group cursor-pointer p-4 rounded-xl bg-gradient-to-br ${item.color} ${item.hoverColor} text-white transition-all duration-300 transform hover:scale-105 hover:shadow-xl animate-fadeInUp border-0 w-full`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex flex-col items-center text-center">
                <div className="text-3xl mb-3 group-hover:scale-110 transition-transform duration-300">
                  {item.icon}
                </div>
                <h4 className="font-semibold text-lg mb-2">
                  {item.title}
                </h4>
                <p className="text-sm opacity-90 leading-relaxed">
                  {item.description}
                </p>
                <div className="mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <svg className="w-5 h-5 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}

QuickNavigationCard.propTypes = {
  navigate: PropTypes.func.isRequired,
};

export default QuickNavigationCard;
