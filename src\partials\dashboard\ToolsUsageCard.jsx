import React, { useState, useEffect } from 'react';

function ToolsUsageCard() {
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [isLoading, setIsLoading] = useState(true);
  const [animatedValues, setAnimatedValues] = useState({});

  // Mock data for tools usage - TODO: Replace with actual API calls // NOSONAR
  const toolsData = {
    '7d': [
      { name: 'Milk Sold Evaluation', usage: 1247, percentage: 87, icon: '🥛', color: 'from-blue-500 to-indigo-600' },
      { name: 'Return Over Feed', usage: 892, percentage: 62, icon: '🌾', color: 'from-emerald-500 to-teal-600' },
      { name: 'Cud Chewing Analysis', usage: 634, percentage: 44, icon: '🐄', color: 'from-amber-500 to-orange-600' },
      { name: 'Profitability Analysis', usage: 1156, percentage: 81, icon: '💰', color: 'from-purple-500 to-pink-600' }
    ],
    '30d': [
      { name: 'Milk Sold Evaluation', usage: 4892, percentage: 89, icon: '🥛', color: 'from-blue-500 to-indigo-600' },
      { name: 'Return Over Feed', usage: 3456, percentage: 67, icon: '🌾', color: 'from-emerald-500 to-teal-600' },
      { name: 'Cud Chewing Analysis', usage: 2134, percentage: 48, icon: '🐄', color: 'from-amber-500 to-orange-600' },
      { name: 'Profitability Analysis', usage: 4123, percentage: 84, icon: '💰', color: 'from-purple-500 to-pink-600' }
    ],
    '90d': [
      { name: 'Milk Sold Evaluation', usage: 14567, percentage: 91, icon: '🥛', color: 'from-blue-500 to-indigo-600' },
      { name: 'Return Over Feed', usage: 10234, percentage: 72, icon: '🌾', color: 'from-emerald-500 to-teal-600' },
      { name: 'Cud Chewing Analysis', usage: 6789, percentage: 52, icon: '🐄', color: 'from-amber-500 to-orange-600' },
      { name: 'Profitability Analysis', usage: 12456, percentage: 87, icon: '💰', color: 'from-purple-500 to-pink-600' }
    ]
  };

  const currentData = toolsData[selectedPeriod];

  const animateNumbers = (data, currentStep, steps, stepDuration) => {
    const progress = currentStep / steps;
    const easeOutQuart = 1 - Math.pow(1 - progress, 4);

    const newAnimatedValues = {};
    data.forEach((tool, index) => {
      newAnimatedValues[index] = Math.floor(tool.usage * easeOutQuart);
    });

    setAnimatedValues(newAnimatedValues);

    if (currentStep < steps) {
      setTimeout(() => animateNumbers(data, currentStep + 1, steps, stepDuration), stepDuration);
    }
  };

  useEffect(() => {
    setIsLoading(true);

    // Simulate loading
    const loadingTimer = setTimeout(() => {
      setIsLoading(false);

      // Animate usage numbers
      const duration = 1500;
      const steps = 50;
      const stepDuration = duration / steps;

      animateNumbers(currentData, 1, steps, stepDuration);
    }, 800);

    return () => clearTimeout(loadingTimer);
  }, [selectedPeriod]);

  const formatNumber = (num) => {
    return num.toLocaleString();
  };

  const getTotalUsage = () => {
    return currentData.reduce((total, tool) => total + tool.usage, 0);
  };

  if (isLoading) {
    return (
      <div className="card">
        <div className="p-6">
          <div className="animate-shimmer h-6 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={`tools-shimmer-${i}`} className="animate-shimmer h-40 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card glow-on-hover">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              Tools Usage Analytics
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              Most popular tools and features used by farmers
            </p>
          </div>
          
          {/* Period Selector */}
          <div className="flex bg-slate-100 dark:bg-slate-700 rounded-lg p-1">
            {['7d', '30d', '90d'].map((period) => (
              <button
                key={period}
                onClick={() => setSelectedPeriod(period)}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-all duration-200 ${
                  selectedPeriod === period
                    ? 'bg-white dark:bg-slate-600 text-indigo-600 dark:text-indigo-400 shadow-sm'
                    : 'text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200'
                }`}
              >
                {period}
              </button>
            ))}
          </div>
        </div>

        {/* Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {currentData.map((tool, index) => (
            <div
              key={tool.name}
              className={`relative overflow-hidden rounded-xl bg-gradient-to-br ${tool.color} p-4 text-white group hover:scale-105 transition-all duration-300 animate-fadeInUp`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute -right-2 -top-2 w-16 h-16 rounded-full bg-white"></div>
                <div className="absolute -left-1 -bottom-1 w-10 h-10 rounded-full bg-white"></div>
              </div>

              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-3">
                  <div className="text-2xl group-hover:scale-110 transition-transform duration-300">
                    {tool.icon}
                  </div>
                  <div className="text-xs opacity-80">
                    {tool.percentage}% adoption
                  </div>
                </div>

                <div className="mb-3">
                  <div className="text-2xl font-bold mb-1">
                    {formatNumber(animatedValues[index] || 0)}
                  </div>
                  <div className="text-xs opacity-90 leading-tight">
                    {tool.name}
                  </div>
                </div>

                {/* Usage Bar */}
                <div className="w-full bg-white/20 rounded-full h-2 overflow-hidden">
                  <div 
                    className="h-2 bg-white rounded-full transition-all duration-1000 ease-out"
                    style={{ 
                      width: `${tool.percentage}%`,
                      animationDelay: `${index * 0.2}s`
                    }}
                  ></div>
                </div>

                {/* Trend Indicator */}
                <div className="mt-2 flex items-center justify-between text-xs opacity-80">
                  <span>Usage</span>
                  <div className="flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L10 4.414 6.707 7.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>+{Math.floor(Math.random() * 20 + 5)}%</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Summary Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-6 border-t border-slate-200 dark:border-slate-700">
          <div className="text-center p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
            <div className="text-xl font-bold text-slate-800 dark:text-slate-100">
              {formatNumber(getTotalUsage())}
            </div>
            <div className="text-xs text-slate-600 dark:text-slate-400">
              Total Usage
            </div>
          </div>
          <div className="text-center p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
            <div className="text-xl font-bold text-emerald-600">
              15
            </div>
            <div className="text-xs text-slate-600 dark:text-slate-400">
              Available Tools
            </div>
          </div>
          <div className="text-center p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
            <div className="text-xl font-bold text-indigo-600">
              {Math.round(currentData.reduce((acc, tool) => acc + tool.percentage, 0) / currentData.length)}%
            </div>
            <div className="text-xs text-slate-600 dark:text-slate-400">
              Avg Adoption
            </div>
          </div>
          <div className="text-center p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
            <div className="text-xl font-bold text-purple-600">
              {currentData.length}
            </div>
            <div className="text-xs text-slate-600 dark:text-slate-400">
              Top Tools
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className="mt-6">
          <button className="w-full btn btn-secondary text-sm">
            View All Tools Analytics
            <svg className="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}

export default ToolsUsageCard;
