import React from 'react';
import PropTypes from 'prop-types';

function PreviewModal({ isOpen, onClose, formData }) {
  if (!isOpen) return null;

  const getTypeDisplay = (type) => {
    switch (type) {
      case 'release-notes':
        return 'Release Notes';
      case 'special-action':
        return 'Special Action';
      case 'marketing-campaign':
        return 'Marketing Campaign';
      default:
        return type;
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <button
          type="button"
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 border-0 p-0 cursor-pointer"
          onClick={onClose}
          aria-label="Close modal"
        ></button>

        {/* Modal panel */}
        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
          {/* Phone mockup */}
          <div className="mx-auto w-64 h-96 bg-gray-900 rounded-3xl p-2 shadow-2xl">
            {/* Phone screen */}
            <div className="w-full h-full bg-white rounded-2xl overflow-hidden flex flex-col">
              {/* Status bar */}
              <div className="bg-gray-100 px-4 py-2 flex justify-between items-center text-xs">
                <span>9:41</span>
                <span>📶 📶 📶 🔋</span>
              </div>

              {/* App header */}
              <div className="bg-indigo-600 text-white px-4 py-3">
                <h3 className="font-semibold text-sm">Dairy Entelligen</h3>
              </div>

              {/* Notification content */}
              <div className="flex-1 p-4 overflow-y-auto">
                {/* Notification type badge */}
                <div className="mb-3">
                  <span className="inline-block px-2 py-1 text-xs font-medium bg-indigo-100 text-indigo-800 rounded-full">
                    {getTypeDisplay(formData.type)}
                  </span>
                </div>

                {/* Title */}
                <h2 className="text-lg font-bold text-gray-900 mb-3 leading-tight">
                  {formData.title || 'Notification Title'}
                </h2>

                {/* Description */}
                <div className="text-sm text-gray-700 mb-4 leading-relaxed">
                  {formData.description ? (
                    <div dangerouslySetInnerHTML={{ __html: formData.description.replace(/\n/g, '<br>') }} />
                  ) : (
                    'Notification description will appear here...'
                  )}
                </div>

                {/* Duration */}
                {(formData.startDate || formData.endDate) && (
                  <div className="text-xs text-gray-500 mb-3">
                    <strong>Duration:</strong> {formData.startDate} to {formData.endDate}
                  </div>
                )}

                {/* Attachments */}
                {formData.attachments && formData.attachments.length > 0 && (
                  <div className="mb-3">
                    <div className="text-xs font-medium text-gray-700 mb-2">Attachments:</div>
                    <div className="space-y-1">
                      {formData.attachments.map((attachment) => (
                        <div key={attachment.id || attachment.name} className="flex items-center text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                          <span className="mr-1">📎</span>
                          {attachment.name}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Segmentation info */}
                <div className="text-xs text-gray-500 border-t pt-2">
                  <div><strong>Target:</strong> {formData.userSegmentation}</div>
                  {formData.country && formData.country.length > 0 && (
                    <div><strong>Countries:</strong> {formData.country.join(', ')}</div>
                  )}
                </div>
              </div>

              {/* Action button */}
              <div className="p-4 border-t">
                <button className="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg text-sm font-medium">
                  View Details
                </button>
              </div>
            </div>
          </div>

          {/* Close button */}
          <div className="mt-6 text-center">
            <button
              onClick={onClose}
              className="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-transparent rounded-md hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-gray-500"
            >
              Close Preview
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

PreviewModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  formData: PropTypes.shape({
    type: PropTypes.string,
    title: PropTypes.string,
    description: PropTypes.string,
    startDate: PropTypes.string,
    endDate: PropTypes.string,
    userSegmentation: PropTypes.string,
    country: PropTypes.arrayOf(PropTypes.string),
    attachments: PropTypes.arrayOf(PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      name: PropTypes.string,
    })),
  }).isRequired,
};

export default PreviewModal;
