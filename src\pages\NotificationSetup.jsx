import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import MultiSelect from '../components/MultiSelect.jsx';
import PreviewModal from '../components/PreviewModal.jsx';
import CancelConfirmDialog from '../components/CancelConfirmDialog.jsx';
import { isNotificationArchived } from '../utils/notificationUtils.js';

const MAX_WORD_COUNT = 10000;
const WARNING_WORD_COUNT = 9000;

const getEditData = (id) => {
  if (id === '1') {
    return {
      type: 'marketing-campaign',
      title: 'New Dairy Analytics Features Available',
      description: 'We are excited to announce new analytics features in the Dairy Entelligen app that will help you track your farm performance more effectively.',
      userSegmentation: 'external',
      country: ['US', 'CA', 'UK'],
      startDate: '2025-07-15',
      endDate: '2025-08-30',
      status: 'draft',
      attachments: [
        {
          id: 1,
          name: 'analytics-guide.pdf',
          size: 2048576, // 2MB
          type: 'application/pdf',
          url: '#'
        }
      ]
    };
  }

  if (id === '2') {
    return {
      type: 'release-notes',
      title: 'System Maintenance Alert',
      description: 'Scheduled maintenance window for server upgrades and performance improvements. Users may experience brief interruptions during this time.',
      userSegmentation: 'external',
      country: ['CA', 'US'],
      startDate: '2025-07-01',
      endDate: '2025-08-15',
      status: 'published', // Published notification
      attachments: []
    };
  }

  return {
    type: 'special-action',
    title: 'Holiday Schedule Changes',
    description: 'Please note the updated holiday schedule for farm operations during the winter season. All staff should review the new protocols.',
    userSegmentation: 'internal',
    country: ['US'],
    startDate: '2024-12-01',
    endDate: '2025-01-15', // Past date to make it archived
    status: 'published', // Will show as archived due to past end date
    attachments: []
  };
};

const validateStartDate = (value, formData, errors, touchedFields) => {
  const newErrors = { ...errors };
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  if (!value) {
    newErrors.startDate = 'Start date is required';
    return newErrors;
  }

  const startDate = new Date(value);
  if (startDate < today) {
    newErrors.startDate = 'Start date cannot be in the past';
    return newErrors;
  }

  delete newErrors.startDate;

  // Also check end date relationship if end date was touched
  if (formData.endDate && touchedFields.has('endDate')) {
    const endDate = new Date(formData.endDate);
    if (endDate <= startDate) {
      newErrors.endDate = 'End date must be after start date';
    } else if (errors.endDate === 'End date must be after start date') {
      delete newErrors.endDate;
    }
  }

  return newErrors;
};

const validateEndDate = (value, formData, errors) => {
  const newErrors = { ...errors };
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  if (!value) {
    newErrors.endDate = 'End date is required';
    return newErrors;
  }

  const endDate = new Date(value);
  if (endDate < today) {
    newErrors.endDate = 'End date cannot be in the past';
    return newErrors;
  }

  if (formData.startDate) {
    const startDate = new Date(formData.startDate);
    if (endDate <= startDate) {
      newErrors.endDate = 'End date must be after start date';
    } else {
      delete newErrors.endDate;
    }
  } else {
    delete newErrors.endDate;
  }

  return newErrors;
};

const validateDateField = (name, value, formData, errors, touchedFields) => {
  if (name === 'startDate') {
    return validateStartDate(value, formData, errors, touchedFields);
  }
  return validateEndDate(value, formData, errors);
};

const validateDescriptionField = (value, errors) => {
  const newErrors = { ...errors };

  if (!value.trim()) {
    newErrors.description = 'Description is required';
  } else {
    const words = value.trim().split(/\s+/).filter(word => word.length > 0);
    const currentWordCount = value.trim() === '' ? 0 : words.length;

    if (currentWordCount > MAX_WORD_COUNT) {
      newErrors.description = `Limit exceeded: Maximum ${MAX_WORD_COUNT.toLocaleString()} words allowed`;
    } else {
      delete newErrors.description;
    }
  }

  return newErrors;
};

const validateRequiredFields = (formData, fieldsToValidate) => {
  const errors = {};

  if (fieldsToValidate.has('type') && !formData.type) {
    errors.type = 'Notification type is required';
  }

  if (fieldsToValidate.has('userSegmentation') && !formData.userSegmentation) {
    errors.userSegmentation = 'User segmentation is required';
  }

  if (fieldsToValidate.has('country') && (!formData.country || formData.country.length === 0)) {
    errors.country = 'At least one country must be selected';
  }

  if (fieldsToValidate.has('title') && !formData.title.trim()) {
    errors.title = 'Title is required';
  }

  return errors;
};

const validateSingleFormDate = (dateValue, fieldName, today) => {
  if (!dateValue) {
    return `${fieldName === 'startDate' ? 'Start' : 'End'} date is required`;
  }

  const date = new Date(dateValue);
  if (date < today) {
    return `${fieldName === 'startDate' ? 'Start' : 'End'} date cannot be in the past`;
  }

  return null;
};

const validateDateRelationship = (formData) => {
  if (!formData.startDate || !formData.endDate) {
    return null;
  }

  const startDate = new Date(formData.startDate);
  const endDate = new Date(formData.endDate);

  return endDate <= startDate ? 'End date must be after start date' : null;
};

const validateFormDates = (formData, fieldsToValidate) => {
  const errors = {};
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  if (fieldsToValidate.has('startDate')) {
    const startDateError = validateSingleFormDate(formData.startDate, 'startDate', today);
    if (startDateError) {
      errors.startDate = startDateError;
    }
  }

  if (fieldsToValidate.has('endDate')) {
    const endDateError = validateSingleFormDate(formData.endDate, 'endDate', today);
    if (endDateError) {
      errors.endDate = endDateError;
    }
  }

  if (fieldsToValidate.has('startDate') || fieldsToValidate.has('endDate')) {
    const relationshipError = validateDateRelationship(formData);
    if (relationshipError) {
      errors.endDate = relationshipError;
    }
  }

  return errors;
};

const validateFormDescription = (formData, fieldsToValidate) => {
  const errors = {};

  if (fieldsToValidate.has('description')) {
    if (!formData.description.trim()) {
      errors.description = 'Description is required';
    } else {
      const words = formData.description.trim().split(/\s+/).filter(word => word.length > 0);
      const currentWordCount = formData.description.trim() === '' ? 0 : words.length;
      if (currentWordCount > MAX_WORD_COUNT) {
        errors.description = `Limit exceeded: Maximum ${MAX_WORD_COUNT.toLocaleString()} words allowed`;
      }
    }
  }

  return errors;
};

const hasAllRequiredFields = (formData) => {
  return (
    formData.type &&
    formData.userSegmentation &&
    formData.country && formData.country.length > 0 &&
    formData.title && formData.title.trim() &&
    formData.startDate &&
    formData.endDate &&
    formData.description && formData.description.trim()
  );
};

const isValidDateRange = (formData) => {
  if (!formData.startDate || !formData.endDate) return false;

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const startDate = new Date(formData.startDate);
  const endDate = new Date(formData.endDate);

  return startDate >= today && endDate >= today && endDate > startDate;
};

const isValidWordCount = (description) => {
  if (!description.trim()) return false;

  const words = description.trim().split(/\s+/).filter(word => word.length > 0);
  const currentWordCount = description.trim() === '' ? 0 : words.length;

  return currentWordCount <= MAX_WORD_COUNT;
};

const getStatusBadgeClass = (isArchived, status) => {
  if (isArchived) {
    return 'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-400';
  }
  if (status === 'published') {
    return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
  }
  return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
};

const getStatusDisplayText = (isArchived, status) => {
  if (isArchived) return 'Archived';
  if (status === 'published') return 'Published';
  return 'Draft';
};

const getPageDescription = (isEdit, isArchived) => {
  if (!isEdit) return 'Set up a new notification for your users';
  if (isArchived) return 'View archived notification details (editing disabled)';
  return 'Update notification details and settings';
};

const getFieldClassName = (baseClass, hasError, isArchived) => {
  let className = baseClass;
  if (hasError) className += ' border-red-500';
  if (isArchived) className += ' opacity-50 cursor-not-allowed';
  return className;
};

const getWordCountColorClass = (wordCount, maxWords, warningWords) => {
  if (wordCount > maxWords) return 'text-red-600';
  if (wordCount > warningWords) return 'text-amber-600';
  return '';
};

function NotificationSetup() {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  const [formData, setFormData] = useState({
    type: 'release-notes',
    userSegmentation: 'internal',
    country: [],
    title: '',
    startDate: '',
    endDate: '',
    description: '',
    attachments: [],
    status: 'draft' // Track notification status
  });

  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const fileInputRef = React.useRef(null);

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [initialFormData, setInitialFormData] = useState(null);
  const [touchedFields, setTouchedFields] = useState(new Set());
  const [isArchived, setIsArchived] = useState(false);

  // TODO: /api/notifications/{id} - Fetch notification data for editing // NOSONAR
  useEffect(() => {
    if (isEdit) {
      setIsLoading(true);
      // Mock data for now
      setTimeout(() => {
        // Simulate different notifications based on ID
        const editData = getEditData(id);
        setFormData(editData);
        setInitialFormData(editData);
        setIsArchived(isNotificationArchived(editData));
        setIsLoading(false);
      }, 1000);
    } else {
      // Set initial form data for new notifications
      const initialData = {
        type: 'release-notes',
        userSegmentation: 'internal',
        country: [],
        title: '',
        startDate: '',
        endDate: '',
        description: '',
        attachments: [],
        status: 'draft'
      };
      setInitialFormData(initialData);
    }
  }, [isEdit, id]);

  // Track changes to determine if there are unsaved changes
  useEffect(() => {
    if (initialFormData) {
      const hasChanges = JSON.stringify(formData) !== JSON.stringify(initialFormData);
      setHasUnsavedChanges(hasChanges);
    }
  }, [formData, initialFormData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Mark field as touched
    setTouchedFields(prev => new Set([...prev, name]));

    // Handle word counting for description
    if (name === 'description') {
      const words = value.trim().split(/\s+/).filter(word => word.length > 0);
      const currentWordCount = value.trim() === '' ? 0 : words.length;
      setWordCount(currentWordCount);
    }

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Real-time validation - only validate fields that have been touched
    const newErrors = { ...errors };

    switch (name) {
      case 'type':
        if (!value) {
          newErrors.type = 'Notification type is required';
        } else {
          delete newErrors.type;
        }
        break;
      case 'userSegmentation':
        if (!value) {
          newErrors.userSegmentation = 'User segmentation is required';
        } else {
          delete newErrors.userSegmentation;
        }
        break;
      case 'title':
        if (!value.trim()) {
          newErrors.title = 'Title is required';
        } else {
          delete newErrors.title;
        }
        break;
      case 'startDate':
      case 'endDate':
        Object.assign(newErrors, validateDateField(name, value, formData, errors, touchedFields));
        break;
      case 'description':
        Object.assign(newErrors, validateDescriptionField(value, errors));
        break;
    }

    setErrors(newErrors);
  };

  const validateForm = () => {
    // Only validate fields that have been touched or for create mode validate all
    const fieldsToValidate = isEdit ? touchedFields : new Set(['type', 'userSegmentation', 'country', 'title', 'startDate', 'endDate', 'description']);

    const newErrors = {
      ...validateRequiredFields(formData, fieldsToValidate),
      ...validateFormDates(formData, fieldsToValidate),
      ...validateFormDescription(formData, fieldsToValidate)
    };

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      // TODO: /api/notifications - Create or update notification // NOSONAR
      console.log('Submitting notification:', formData); // NOSONAR
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Navigate back to notifications list
      navigate('/notifications');
    } catch (error) {
      console.error('Error saving notification:', error); // NOSONAR
      // Handle error
    } finally {
      setIsLoading(false);
    }
  };





  // Check if publish button should be enabled
  const isPublishEnabled = () => {
    // Disable publish button for archived notifications
    if (isArchived) {
      return false;
    }

    // For create mode, check all required fields
    if (!isEdit) {
      if (!hasAllRequiredFields(formData)) {
        return false;
      }

      if (!isValidDateRange(formData)) {
        return false;
      }

      if (!isValidWordCount(formData.description)) {
        return false;
      }

      return true;
    }

    // For edit mode: check if there are any validation errors in the current errors state
    // (which only contains errors for touched fields)
    if (Object.keys(errors).length > 0) {
      return false;
    }

    // For edit mode:
    // - If notification is draft: enable when no validation errors
    // - If notification is published: enable only when changes were made AND no validation errors
    return formData.status === 'draft' || (formData.status === 'published' && hasUnsavedChanges);
  };

  // Check if draft button should be enabled
  const isDraftEnabled = () => {
    if (isEdit) {
      // For edit mode: enable only if changes were made
      return hasUnsavedChanges;
    } else {
      // For create mode: always enabled
      return true;
    }
  };

  const handlePublish = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // TODO: /api/notifications/publish - Publish notification // NOSONAR
      console.log('Publishing notification:', formData); // NOSONAR

      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Navigate back to notifications list
      navigate('/notifications');
    } catch (error) {
      console.error('Error publishing notification:', error); // NOSONAR
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveDraft = async () => {
    setIsLoading(true);

    try {
      // TODO: /api/notifications/draft - Save as draft // NOSONAR
      console.log('Saving draft:', formData); // NOSONAR

      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Navigate back to notifications list
      navigate('/notifications');
    } catch (error) {
      console.error('Error saving draft:', error); // NOSONAR
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreview = () => {
    setShowPreview(true);
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      setShowCancelDialog(true);
    } else {
      navigate('/notifications');
    }
  };

  const confirmCancel = () => {
    setShowCancelDialog(false);
    navigate('/notifications');
  };

  const cancelCancel = () => {
    setShowCancelDialog(false);
  };

  // File upload handlers
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleFileInput = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const validateFileType = (file) => {
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
    return allowedTypes.includes(file.type);
  };

  const validateFileSize = (file, validFiles) => {
    const currentTotalSize = formData.attachments.reduce((total, att) => total + att.size, 0);
    const validFilesSize = validFiles.reduce((total, validFile) => total + validFile.size, 0);
    const newTotalSize = currentTotalSize + validFilesSize + file.size;
    const maxTotalSize = 20 * 1024 * 1024; // 20MB in bytes

    if (newTotalSize <= maxTotalSize) {
      return { valid: true };
    }

    const remainingSize = maxTotalSize - currentTotalSize - validFilesSize;
    const remainingSizeMB = (remainingSize / (1024 * 1024)).toFixed(1);
    return {
      valid: false,
      error: `${file.name}: File too large. Only ${remainingSizeMB}MB remaining of 20MB limit.`
    };
  };

  const validateSingleFile = (file, validFiles) => {
    if (!validateFileType(file)) {
      return { valid: false, error: `${file.name}: Only PDF, JPEG, JPG, and PNG files are allowed.` };
    }

    return validateFileSize(file, validFiles);
  };

  const handleFiles = (files) => {
    const validFiles = [];
    const errors = [];

    Array.from(files).forEach(file => {
      const validation = validateSingleFile(file, validFiles);

      if (validation.valid) {
        validFiles.push(file);
      } else {
        errors.push(validation.error);
      }
    });

    // Show errors if any
    if (errors.length > 0) {
      alert(errors.join('\n'));
    }

    // Process only valid files
    validFiles.forEach(file => {
      // Simulate upload progress
      const fileId = Date.now() + Math.random();
      setUploadProgress(prev => ({ ...prev, [fileId]: 0 }));

      // Mock upload progress
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[fileId] || 0;
          if (currentProgress >= 100) {
            clearInterval(interval);
            // Add file to attachments
            setFormData(prevData => ({
              ...prevData,
              attachments: [...prevData.attachments, {
                id: fileId,
                name: file.name,
                size: file.size,
                type: file.type,
                url: URL.createObjectURL(file)
              }]
            }));
            // Remove from progress tracking
            setUploadProgress(prev => {
              const newProgress = { ...prev };
              delete newProgress[fileId];
              return newProgress;
            });
            return prev;
          }
          return { ...prev, [fileId]: currentProgress + 10 };
        });
      }, 200);
    });
  };

  const removeAttachment = (attachmentId) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter(att => att.id !== attachmentId)
    }));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  // Country options
  const countryOptions = [
    { value: 'US', label: 'United States' },
    { value: 'CA', label: 'Canada' },
    { value: 'UK', label: 'United Kingdom' },
    { value: 'DE', label: 'Germany' },
    { value: 'FR', label: 'France' },
    { value: 'JP', label: 'Japan' },
    { value: 'AU', label: 'Australia' },
    { value: 'IN', label: 'India' },
    { value: 'BR', label: 'Brazil' },
    { value: 'MX', label: 'Mexico' },
    { value: 'IT', label: 'Italy' },
    { value: 'ES', label: 'Spain' },
    { value: 'NL', label: 'Netherlands' },
    { value: 'SE', label: 'Sweden' },
    { value: 'NO', label: 'Norway' },
    { value: 'DK', label: 'Denmark' },
    { value: 'FI', label: 'Finland' },
    { value: 'CH', label: 'Switzerland' },
    { value: 'AT', label: 'Austria' },
    { value: 'BE', label: 'Belgium' }
  ];

  // Handle country selection
  const handleCountryChange = (value) => {
    // Mark country field as touched
    setTouchedFields(prev => new Set([...prev, 'country']));

    const newCountryList = formData.country.includes(value)
      ? formData.country.filter(item => item !== value)
      : [...formData.country, value];

    setFormData(prev => ({
      ...prev,
      country: newCountryList
    }));

    // Real-time validation for country selection
    const newErrors = { ...errors };
    if (newCountryList.length === 0) {
      newErrors.country = 'At least one country must be selected';
    } else {
      delete newErrors.country;
    }
    setErrors(newErrors);
  };

  if (isLoading && isEdit) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <span className="ml-2 text-slate-600 dark:text-slate-400">Loading notification...</span>
      </div>
    );
  }

  return (
    <>
      {/* Page header */}
      <div className="mb-8">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl md:text-3xl text-slate-800 dark:text-slate-100 font-bold">
            {isEdit ? 'Edit Notification' : 'Create New Notification'}
          </h1>
          {isEdit && formData.status && (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(isArchived, formData.status)}`}>
              {getStatusDisplayText(isArchived, formData.status)}
            </span>
          )}
        </div>
        <p className="text-slate-600 dark:text-slate-400 mt-2">
          {getPageDescription(isEdit, isArchived)}
        </p>
      </div>

      {/* Notification Setup Form */}
      <div className="bg-white dark:bg-slate-800 shadow-lg rounded-sm border border-slate-200 dark:border-slate-700">
        <div className="px-5 py-4">
          {/* Archived Notice */}
          {isArchived && (
            <div className="mb-6 p-4 bg-slate-50 dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600 rounded-lg">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-slate-500 dark:text-slate-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-slate-800 dark:text-slate-100">Archived Notification</h3>
                  <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                    This notification is archived because its end date has passed. Form fields are disabled, but you can still save as draft, preview, or publish with updated dates.
                  </p>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Notification Type */}
            <div>
              <label htmlFor="notification-type" className="block text-sm font-medium text-slate-800 dark:text-slate-100 mb-2">
                Notification Type *
              </label>
              <select
                id="notification-type"
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                disabled={isArchived}
                className={getFieldClassName('form-select w-full', errors.type, isArchived)}
              >
                <option value="release-notes">Release notes</option>
                <option value="special-action">Special action</option>
                <option value="marketing-campaign">Marketing campaign</option>
              </select>
              {errors.type && (
                <p className="mt-1 text-sm text-red-600">{errors.type}</p>
              )}
            </div>

            {/* User Segmentation */}
            <div>
              <label htmlFor="user-segmentation" className="block text-sm font-medium text-slate-800 dark:text-slate-100 mb-2">
                User Segmentation *
              </label>
              <select
                id="user-segmentation"
                name="userSegmentation"
                value={formData.userSegmentation}
                onChange={handleInputChange}
                disabled={isArchived}
                className={getFieldClassName('form-select w-full', errors.userSegmentation, isArchived)}
              >
                <option value="internal">Internal</option>
                <option value="external">External</option>
              </select>
              {errors.userSegmentation && (
                <p className="mt-1 text-sm text-red-600">{errors.userSegmentation}</p>
              )}
            </div>

            {/* Country */}
            <div>
              <label htmlFor="country-select" className="block text-sm font-medium text-slate-800 dark:text-slate-100 mb-2">
                Country *
              </label>
              <MultiSelect
                id="country-select"
                options={countryOptions}
                selectedValues={formData.country}
                onChange={handleCountryChange}
                placeholder="Select"
                disabled={isArchived}
                className={`w-full ${isArchived ? 'opacity-50 cursor-not-allowed' : ''}`}
              />
              {errors.country && (
                <p className="mt-1 text-sm text-red-600">{errors.country}</p>
              )}
            </div>

            {/* Title */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-slate-800 dark:text-slate-100 mb-2">
                Title *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter your subject here…"
                disabled={isArchived}
                className={getFieldClassName('form-input w-full', errors.title, isArchived)}
              />
              {errors.title && (
                <p className="mt-1 text-sm text-red-600">{errors.title}</p>
              )}
            </div>

            {/* Duration */}
            <div>
              <div className="block text-sm font-medium text-slate-800 dark:text-slate-100 mb-2">
                Duration *
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="startDate" className="block text-xs text-slate-600 dark:text-slate-400 mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    id="startDate"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleInputChange}
                    disabled={isArchived}
                    className={getFieldClassName('form-input w-full', errors.startDate, isArchived)}
                  />
                  {errors.startDate && (
                    <p className="mt-1 text-sm text-red-600">{errors.startDate}</p>
                  )}
                </div>
                <div>
                  <label htmlFor="endDate" className="block text-xs text-slate-600 dark:text-slate-400 mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    id="endDate"
                    name="endDate"
                    value={formData.endDate}
                    onChange={handleInputChange}
                    disabled={isArchived}
                    className={getFieldClassName('form-input w-full', errors.endDate, isArchived)}
                  />
                  {errors.endDate && (
                    <p className="mt-1 text-sm text-red-600">{errors.endDate}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-slate-800 dark:text-slate-100 mb-2">
                Description *
              </label>
              <div className="relative">
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter your notification description..."
                  rows={8}
                  disabled={isArchived}
                  className={getFieldClassName('form-textarea w-full resize-none', errors.description, isArchived)}
                  style={{ direction: 'ltr', textAlign: 'left' }}
                />
                <div className="absolute bottom-3 right-3 text-xs text-slate-500 dark:text-slate-400 bg-white dark:bg-slate-800 px-2 py-1 rounded">
                  <span className={getWordCountColorClass(wordCount, MAX_WORD_COUNT, WARNING_WORD_COUNT)}>
                    {wordCount.toLocaleString()} / {MAX_WORD_COUNT.toLocaleString()} words
                  </span>
                </div>
              </div>
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
            </div>



            {/* Attachments */}
            <div>
              <label htmlFor="file-upload" className="block text-sm font-medium text-slate-800 dark:text-slate-100 mb-2">
                Attachments
              </label>
              <p className="text-xs text-slate-600 dark:text-slate-400 mb-4">
                Upload PDF, JPEG, JPG, or PNG files. Total upload limit: 20MB for all files combined.
              </p>

              {/* File Upload Area */}
              <div
                className={`relative border-2 border-dashed rounded-lg p-6 transition-all duration-300 ${
                  isArchived
                    ? 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800 opacity-50 cursor-not-allowed'
                    : dragActive
                    ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20'
                    : 'border-slate-300 dark:border-slate-600 hover:border-indigo-400 dark:hover:border-indigo-500'
                }`}
                onDragEnter={!isArchived ? handleDrag : undefined}
                onDragLeave={!isArchived ? handleDrag : undefined}
                onDragOver={!isArchived ? handleDrag : undefined}
                onDrop={!isArchived ? handleDrop : undefined}
              >
                <div className="text-center">
                  <svg
                    className="mx-auto h-12 w-12 text-slate-400 dark:text-slate-500"
                    stroke="currentColor"
                    fill="none"
                    viewBox="0 0 48 48"
                  >
                    <path
                      d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                      strokeWidth={2}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <div className="mt-4">
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      <span className="font-medium">Drag and drop files here</span> or
                    </p>
                    <button
                      type="button"
                      onClick={openFileDialog}
                      disabled={isArchived}
                      className={`mt-2 btn btn-secondary text-sm ${isArchived ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      Browse Files
                    </button>
                  </div>
                </div>

                {/* Hidden file input */}
                <input
                  id="file-upload"
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={handleFileInput}
                  className="hidden"
                />
              </div>

              {/* Upload Progress */}
              {Object.keys(uploadProgress).length > 0 && (
                <div className="mt-4 space-y-2">
                  {Object.entries(uploadProgress).map(([fileId, progress]) => (
                    <div key={fileId} className="bg-slate-50 dark:bg-slate-700 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-slate-600 dark:text-slate-400">Uploading...</span>
                        <span className="text-sm font-medium text-slate-800 dark:text-slate-100">{progress}%</span>
                      </div>
                      <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-indigo-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Uploaded Files */}
              {formData.attachments.length > 0 && (
                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-slate-800 dark:text-slate-100">
                      Uploaded Files ({formData.attachments.length})
                    </h4>
                    <div className="text-xs text-slate-500 dark:text-slate-400">
                      {formatFileSize(formData.attachments?.reduce((total, att) => total + att.size, 0) || 0)} / 20MB
                    </div>
                  </div>
                  {formData.attachments.map((attachment) => (
                    <div
                      key={attachment.id}
                      className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700 rounded-lg group hover:bg-slate-100 dark:hover:bg-slate-600 transition-all duration-200"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          {attachment.type?.startsWith('image/') ? (
                            <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-slate-800 dark:text-slate-100">
                            {attachment.name}
                          </p>
                          <p className="text-xs text-slate-500 dark:text-slate-400">
                            {formatFileSize(attachment.size)}
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeAttachment(attachment.id)}
                        className="p-1 text-slate-400 hover:text-red-500 dark:hover:text-red-400 transition-colors duration-200 opacity-0 group-hover:opacity-100"
                        title="Remove file"
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-between items-center pt-6 border-t border-slate-200 dark:border-slate-700">
              <div className="flex space-x-3 mb-4 sm:mb-0">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handlePreview}
                  className="btn bg-blue-500 hover:bg-blue-600 text-white"
                >
                  Preview
                </button>
                <button
                  type="button"
                  onClick={handleSaveDraft}
                  disabled={isLoading || !isDraftEnabled()}
                  className="btn bg-amber-500 hover:bg-amber-600 text-white disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Saving...' : 'Draft'}
                </button>
              </div>
              <button
                type="button"
                onClick={handlePublish}
                disabled={isLoading || !isPublishEnabled()}
                title={isArchived ? 'Cannot publish archived notifications' : ''}
                className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Publishing...' : 'Publish'}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Preview Modal */}
      <PreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        formData={formData}
      />

      {/* Cancel Confirmation Dialog */}
      <CancelConfirmDialog
        isOpen={showCancelDialog}
        onConfirm={confirmCancel}
        onCancel={cancelCancel}
      />
    </>
  );
}

export default NotificationSetup;
