import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import NotificationsList from '../partials/NotificationsList.jsx';
import MultiSelect from '../components/MultiSelect.jsx';

function NotificationManagement() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState([]);
  const [userSegmentationFilter, setUserSegmentationFilter] = useState([]);
  const [countryFilter, setCountryFilter] = useState([]);
  const [statusFilter, setStatusFilter] = useState([]);
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });

  const handleCreateNotification = () => {
    navigate('/notifications/setup');
  };

  // Multi-select handlers
  const handleTypeFilterChange = (value) => {
    setTypeFilter(prev =>
      prev.includes(value)
        ? prev.filter(item => item !== value)
        : [...prev, value]
    );
  };

  const handleUserSegmentationChange = (value) => {
    setUserSegmentationFilter(prev =>
      prev.includes(value)
        ? prev.filter(item => item !== value)
        : [...prev, value]
    );
  };

  const handleCountryChange = (value) => {
    setCountryFilter(prev =>
      prev.includes(value)
        ? prev.filter(item => item !== value)
        : [...prev, value]
    );
  };

  const handleStatusFilterChange = (value) => {
    setStatusFilter(prev =>
      prev.includes(value)
        ? prev.filter(item => item !== value)
        : [...prev, value]
    );
  };

  const handleDateRangeChange = (field, value) => {
    setDateRange(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const clearAllFilters = () => {
    setSearchTerm('');
    setTypeFilter([]);
    setUserSegmentationFilter([]);
    setCountryFilter([]);
    setStatusFilter([]);
    setDateRange({ startDate: '', endDate: '' });
  };

  // Filter options
  const typeOptions = [
    { value: 'release-notes', label: 'Release Notes' },
    { value: 'special-actions', label: 'Special Actions' },
    { value: 'marketing-campaigns', label: 'Marketing Campaigns' }
  ];

  const userSegmentationOptions = [
    { value: 'internal', label: 'Internal' },
    { value: 'external', label: 'External' }
  ];

  const countryOptions = [
    { value: 'US', label: 'United States' },
    { value: 'CA', label: 'Canada' },
    { value: 'UK', label: 'United Kingdom' },
    { value: 'DE', label: 'Germany' },
    { value: 'FR', label: 'France' },
    { value: 'JP', label: 'Japan' },
    { value: 'AU', label: 'Australia' },
    { value: 'IN', label: 'India' },
    { value: 'BR', label: 'Brazil' },
    { value: 'MX', label: 'Mexico' }
  ];

  const statusOptions = [
    { value: 'published', label: 'Published' },
    { value: 'draft', label: 'Draft' },
    { value: 'archived', label: 'Archived' }
  ];

  // Parse date from input
  const parseDateFromInput = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toISOString().split('T')[0];
  };

  return (
    <div className="animate-fadeInUp">
      {/* Page header */}
      <div className="mb-8">
        {/* Title and Description */}
        <div className="mb-6 animate-fadeInLeft">
          <h1 className="text-2xl md:text-3xl text-slate-800 dark:text-slate-100 font-bold mb-2">
            📢 Notifications Management
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            Create, manage, and track notifications sent to app users
          </p>
        </div>

        {/* Create Button */}
        <div className="flex justify-end mb-6 animate-fadeInRight">
          <button
            onClick={handleCreateNotification}
            className="btn btn-primary glow-on-hover"
          >
            <svg className="w-4 h-4 fill-current opacity-75 shrink-0" viewBox="0 0 16 16">
              <path d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
            </svg>
            <span className="ml-2">Create Notification</span>
          </button>
        </div>

        {/* Filters */}
        <div className="space-y-4 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
          {/* First Row - Search and Clear */}
          <div className="flex flex-wrap gap-4 items-center">
            {/* Search */}
            <div className="relative input-enhanced flex-1 min-w-64">
              <input
                type="text"
                placeholder="Search by keyword..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pl-10 pr-4 w-full transition-all duration-300"
              />
              <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                <svg className="w-4 h-4 fill-current text-slate-500 dark:text-slate-400" viewBox="0 0 16 16">
                  <path d="M7 14c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zM7 2C4.243 2 2 4.243 2 7s2.243 5 5 5 5-2.243 5-5-2.243-5-5-5z" />
                  <path d="M15.707 14.293L13.314 11.9a8.019 8.019 0 01-1.414 1.414l2.393 2.393a.997.997 0 001.414 0 .999.999 0 000-1.414z" />
                </svg>
              </div>
            </div>

            {/* Clear All Filters */}
            <button
              onClick={clearAllFilters}
              className="btn btn-secondary text-sm"
            >
              Clear All Filters
            </button>
          </div>

          {/* Second Row - Multi-select Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Notification Type */}
            <div>
              <label htmlFor="notification-type-filter" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Notification Type
              </label>
              <MultiSelect
                id="notification-type-filter"
                options={typeOptions}
                selectedValues={typeFilter}
                onChange={handleTypeFilterChange}
                placeholder="Select types..."
                className="w-full"
              />
            </div>

            {/* User Segmentation */}
            <div>
              <label htmlFor="user-segmentation-filter" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                User Segmentation
              </label>
              <MultiSelect
                id="user-segmentation-filter"
                options={userSegmentationOptions}
                selectedValues={userSegmentationFilter}
                onChange={handleUserSegmentationChange}
                placeholder="Select segmentation..."
                className="w-full"
              />
            </div>

            {/* Country */}
            <div>
              <label htmlFor="country-filter" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Country
              </label>
              <MultiSelect
                id="country-filter"
                options={countryOptions}
                selectedValues={countryFilter}
                onChange={handleCountryChange}
                placeholder="Select countries..."
                className="w-full"
              />
            </div>

            {/* Status Filter */}
            <div>
              <label htmlFor="status-filter" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Status
              </label>
              <MultiSelect
                id="status-filter"
                options={statusOptions}
                selectedValues={statusFilter}
                onChange={handleStatusFilterChange}
                placeholder="Select status..."
                className="w-full"
              />
            </div>
          </div>

          {/* Third Row - Date Range */}
          <div>
            <div className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Duration
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md">
              <div>
                <label htmlFor="start-date-filter" className="block text-xs text-slate-600 dark:text-slate-400 mb-1">
                  From Date
                </label>
                <input
                  id="start-date-filter"
                  type="date"
                  value={parseDateFromInput(dateRange.startDate)}
                  onChange={(e) => handleDateRangeChange('startDate', e.target.value ? new Date(e.target.value).toISOString() : '')}
                  className="form-input w-full"
                />
              </div>
              <div>
                <label htmlFor="end-date-filter" className="block text-xs text-slate-600 dark:text-slate-400 mb-1">
                  To Date
                </label>
                <input
                  id="end-date-filter"
                  type="date"
                  value={parseDateFromInput(dateRange.endDate)}
                  onChange={(e) => handleDateRangeChange('endDate', e.target.value ? new Date(e.target.value).toISOString() : '')}
                  className="form-input w-full"
                />
              </div>
            </div>
          </div>

          {/* Active Filters Display */}
          {(typeFilter.length > 0 || userSegmentationFilter.length > 0 || countryFilter.length > 0 || statusFilter.length > 0 || dateRange.startDate || dateRange.endDate || searchTerm) && (
            <div className="flex flex-wrap gap-2 pt-2 border-t border-slate-200 dark:border-slate-700">
              <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Active filters:</span>

              {searchTerm && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  Search: "{searchTerm}"
                  <button onClick={() => setSearchTerm('')} className="ml-1 text-blue-600 hover:text-blue-800">×</button>
                </span>
              )}

              {typeFilter.map(type => (
                <span key={type} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                  Type: {typeOptions.find(opt => opt.value === type)?.label}
                  <button onClick={() => handleTypeFilterChange(type)} className="ml-1 text-purple-600 hover:text-purple-800">×</button>
                </span>
              ))}

              {userSegmentationFilter.map(seg => (
                <span key={seg} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  Segment: {userSegmentationOptions.find(opt => opt.value === seg)?.label}
                  <button onClick={() => handleUserSegmentationChange(seg)} className="ml-1 text-green-600 hover:text-green-800">×</button>
                </span>
              ))}

              {countryFilter.map(country => (
                <span key={country} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                  Country: {countryOptions.find(opt => opt.value === country)?.label}
                  <button onClick={() => handleCountryChange(country)} className="ml-1 text-orange-600 hover:text-orange-800">×</button>
                </span>
              ))}

              {statusFilter.map(status => (
                <span key={status} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-200">
                  Status: {statusOptions.find(opt => opt.value === status)?.label}
                  <button onClick={() => handleStatusFilterChange(status)} className="ml-1 text-slate-600 hover:text-slate-800">×</button>
                </span>
              ))}

              {(dateRange.startDate || dateRange.endDate) && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                  Date: {dateRange.startDate ? new Date(dateRange.startDate).toLocaleDateString() : '...'} - {dateRange.endDate ? new Date(dateRange.endDate).toLocaleDateString() : '...'}
                  <button onClick={() => setDateRange({ startDate: '', endDate: '' })} className="ml-1 text-indigo-600 hover:text-indigo-800">×</button>
                </span>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Notifications List */}
      <div className="animate-fadeInUp" style={{animationDelay: '0.3s'}}>
        <NotificationsList
          searchTerm={searchTerm}
          typeFilter={typeFilter}
          userSegmentationFilter={userSegmentationFilter}
          countryFilter={countryFilter}
          statusFilter={statusFilter}
          dateRange={dateRange}
        />
      </div>
    </div>
  );
}

export default NotificationManagement;
