import React from 'react';
import PropTypes from 'prop-types';

const getTrendColor = (trend) => {
  if (trend === 'up') return 'text-emerald-600';
  if (trend === 'down') return 'text-red-600';
  return 'text-slate-600';
};

const getTrendBackground = (trend) => {
  if (trend === 'up') return 'bg-emerald-100 dark:bg-emerald-900/30';
  if (trend === 'down') return 'bg-red-100 dark:bg-red-900/30';
  return 'bg-slate-100 dark:bg-slate-700';
};

function DairyMetricsCard({ title, value, unit, change, trend, icon }) {
  const trendColor = getTrendColor(trend);
  const trendBg = getTrendBackground(trend);

  return (
    <div className="card glow-on-hover group">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="text-2xl animate-pulse-gentle">{icon}</div>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${trendBg} ${trendColor} transition-all duration-300 group-hover:scale-110`}>
            {change}
          </div>
        </div>

        {/* Value */}
        <div className="mb-2">
          <div className="text-3xl font-bold text-slate-800 dark:text-slate-100 transition-all duration-300 group-hover:text-indigo-600 dark:group-hover:text-indigo-400">
            {value}
          </div>
          <div className="text-sm text-slate-500 dark:text-slate-400">
            {unit}
          </div>
        </div>

        {/* Title */}
        <div className="text-sm font-medium text-slate-600 dark:text-slate-400">
          {title}
        </div>

        {/* Progress Bar */}
        <div className="mt-4">
          <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2 overflow-hidden">
            <div 
              className="h-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full transition-all duration-1000 ease-out"
              style={{ width: trend === 'up' ? '75%' : trend === 'down' ? '45%' : '60%' }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
}

DairyMetricsCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  unit: PropTypes.string.isRequired,
  change: PropTypes.string.isRequired,
  trend: PropTypes.oneOf(['up', 'down', 'stable']).isRequired,
  icon: PropTypes.string.isRequired,
};

export default DairyMetricsCard;
