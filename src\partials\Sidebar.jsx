import React, { useState, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

import SidebarBackdrop from './sidebar/SidebarBackdrop.jsx';
import SidebarHeader from './sidebar/SidebarHeader.jsx';
import SidebarDashboardLinks from './sidebar/SidebarDashboardLinks.jsx';
import SidebarEcommerceLinks from './sidebar/SidebarEcommerceLinks.jsx';
import SidebarCommunityLinks from './sidebar/SidebarCommunityLinks.jsx';
import SidebarFinanceLinks from './sidebar/SidebarFinanceLinks.jsx';
import SidebarJobBoardLinks from './sidebar/SidebarJobBoardLinks.jsx';
import SidebarTasksLinks from './sidebar/SidebarTasksLinks.jsx';
import SidebarMessagesLinks from './sidebar/SidebarMessagesLinks.jsx';
import SidebarInboxLinks from './sidebar/SidebarInboxLinks.jsx';
import SidebarCalendarLinks from './sidebar/SidebarCalendarLinks.jsx';
import SidebarCampaignsLinks from './sidebar/SidebarCampaignsLinks.jsx';
import SidebarNotificationLinks from './sidebar/SidebarNotificationLinks.jsx';
import SidebarSettingsLinks from './sidebar/SidebarSettingsLinks.jsx';
import SidebarUtilityLinks from './sidebar/SidebarUtilityLinks.jsx';
import SidebarAuthenticationLinks from './sidebar/SidebarAuthenticationLinks.jsx';
import SidebarOnboardingLinks from './sidebar/SidebarOnboardingLinks.jsx';
import SidebarComponentsLinks from './sidebar/SidebarComponentsLinks.jsx';
import {AuthenticatedTemplate} from '@azure/msal-react';

function Sidebar({ sidebarOpen, setSidebarOpen }) {
  const location = useLocation();
  const { pathname } = location;

  const trigger = useRef(null);
  const sidebar = useRef(null);

  const storedSidebarExpanded = localStorage.getItem('sidebar-expanded');
  const [sidebarExpanded, setSidebarExpanded] = useState(storedSidebarExpanded === null ? false : storedSidebarExpanded === 'true');

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (!sidebar.current || !trigger.current) return;
      if (!sidebarOpen || sidebar.current.contains(target) || trigger.current.contains(target)) return;
      setSidebarOpen(false);
    };
    document.addEventListener('click', clickHandler);
    return () => document.removeEventListener('click', clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!sidebarOpen || keyCode !== 27) return;
      setSidebarOpen(false);
    };
    document.addEventListener('keydown', keyHandler);
    return () => document.removeEventListener('keydown', keyHandler);
  });

  useEffect(() => {
    localStorage.setItem('sidebar-expanded', sidebarExpanded);
    if (sidebarExpanded) {
      document.querySelector('body').classList.add('sidebar-expanded');
    } else {
      document.querySelector('body').classList.remove('sidebar-expanded');
    }
  }, [sidebarExpanded]);

  return (
    <div>
      {/* Sidebar backdrop (mobile only) */}
      <SidebarBackdrop sidebarOpen={sidebarOpen}></SidebarBackdrop>

      {/* Sidebar */}
      <div
        id="sidebar"
        ref={sidebar}
        className={`flex flex-col absolute z-40 left-0 top-0 lg:static lg:left-auto lg:top-auto lg:translate-x-0 h-screen overflow-y-scroll lg:overflow-y-auto no-scrollbar w-64 lg:w-20 lg:sidebar-expanded:!w-64 2xl:!w-64 shrink-0 bg-slate-800 p-4 transition-all duration-200 ease-in-out ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-64'
        }`}
      >
        {/* Sidebar header */}
        <SidebarHeader sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} trigger={trigger}></SidebarHeader>

        {/* Links */}
        <div className="space-y-8">
          {/* Pages group */}
          <div>
            <h3 className="text-xs uppercase text-slate-500 font-semibold pl-3">
              <span className="hidden lg:block lg:sidebar-expanded:hidden 2xl:hidden text-center w-6" aria-hidden="true">
                •••
              </span>
              <span className="lg:hidden lg:sidebar-expanded:block 2xl:block">Pages</span>
            </h3>
            <ul className="mt-3">
            <AuthenticatedTemplate>
              {/* Dashboard */}
              <SidebarDashboardLinks pathname={pathname} sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded}></SidebarDashboardLinks>
              {/* E-Commerce */}
              {/*<SidebarEcommerceLinks pathname={pathname} sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded}></SidebarEcommerceLinks>*/}
              {/* Community */}
              <SidebarCommunityLinks pathname={pathname} sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded}></SidebarCommunityLinks>
              {/* Notifications */}
              <SidebarNotificationLinks pathname={pathname}></SidebarNotificationLinks>
              {/* Finance */}
              {/*<SidebarFinanceLinks pathname={pathname} sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded}></SidebarFinanceLinks>*/}
              {/* Job Board */}
              {/*<SidebarJobBoardLinks pathname={pathname} sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded}></SidebarJobBoardLinks>*/}
              {/* Tasks */}
              {/*<SidebarTasksLinks pathname={pathname} sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded}></SidebarTasksLinks>*/}
              {/* Messages */}
              {/*<SidebarMessagesLinks pathname={pathname}></SidebarMessagesLinks>*/}
              {/* Inbox */}
              {/*<SidebarInboxLinks pathname={pathname}></SidebarInboxLinks>*/}
              {/* Calendar */}
              {/*<SidebarCalendarLinks pathname={pathname}></SidebarCalendarLinks>*/}
              {/* Campaigns */}
              {/*<SidebarCampaignsLinks pathname={pathname}></SidebarCampaignsLinks>*/}
              {/* Settings */}
              {/*<SidebarSettingsLinks pathname={pathname} sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded}></SidebarSettingsLinks>*/}
              {/* Utility */}
              {/*<SidebarUtilityLinks pathname={pathname} sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded}></SidebarUtilityLinks>*/}
            </AuthenticatedTemplate>
            </ul>
          </div>
          {/* More group */}
          <div>
            <h3 className="text-xs uppercase text-slate-500 font-semibold pl-3">
              <span className="hidden lg:block lg:sidebar-expanded:hidden 2xl:hidden text-center w-6" aria-hidden="true">
                •••
              </span>
              <span className="lg:hidden lg:sidebar-expanded:block 2xl:block">More</span>
            </h3>
            <ul className="mt-3">
              {/* Authentication */}
              <SidebarAuthenticationLinks sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded}></SidebarAuthenticationLinks>
              {/* Onboarding */}
              {/*<SidebarOnboardingLinks sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded}></SidebarOnboardingLinks>*/}
              {/* Components */}
              {/*<SidebarComponentsLinks pathname={pathname} sidebarExpanded={sidebarExpanded} setSidebarExpanded={setSidebarExpanded}></SidebarComponentsLinks>*/}
            </ul>
          </div>
        </div>

        {/* Expand / collapse button */}
        <div className="pt-3 hidden lg:inline-flex 2xl:hidden justify-end mt-auto">
          <div className="px-3 py-2">
            <button onClick={() => setSidebarExpanded(!sidebarExpanded)}>
              <span className="sr-only">Expand / collapse sidebar</span>
              <svg className="w-6 h-6 fill-current sidebar-expanded:rotate-180" viewBox="0 0 24 24">
                <path className="text-slate-400" d="M19.586 11l-5-5L16 4.586 23.414 12 16 19.414 14.586 18l5-5H7v-2z" />
                <path className="text-slate-600" d="M3 23H1V1h2z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Sidebar;
