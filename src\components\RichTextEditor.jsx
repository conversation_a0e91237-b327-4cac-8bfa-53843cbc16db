import React, { useState, useRef } from 'react';
import PropTypes from 'prop-types';

const getWordCountColor = (wordCount, maxWords) => {
  if (wordCount > maxWords) return 'text-red-600';
  if (wordCount > maxWords * 0.9) return 'text-amber-600';
  return 'text-slate-500 dark:text-slate-400';
};

function RichTextEditor({ value, onChange, placeholder, error, maxWords = 10000 }) {
  const editorRef = useRef(null);
  const [wordCount, setWordCount] = useState(0);

  const handleInput = (e) => {
    const content = e.target.value;
    const words = content.trim().split(/\s+/).filter(word => word.length > 0);
    const currentWordCount = content.trim() === '' ? 0 : words.length;

    setWordCount(currentWordCount);
    onChange(content);
  };

  const execCommand = (command, value = null) => {
    // NOSONAR - Using deprecated execCommand for rich text editing compatibility
    document.execCommand(command, false, value);
    editorRef.current?.focus();
  };

  const handleKeyDown = (e) => {
    // Prevent typing if word limit exceeded
    if (wordCount >= maxWords && e.key !== 'Backspace' && e.key !== 'Delete' && !e.ctrlKey && !e.metaKey) {
      e.preventDefault();
    }
  };

  const formatButtons = [
    { command: 'bold', icon: 'B', title: 'Bold', style: 'font-bold' },
    { command: 'italic', icon: 'I', title: 'Italic', style: 'italic' },
    { command: 'underline', icon: 'U', title: 'Underline', style: 'underline' },
    { command: 'strikeThrough', icon: 'S', title: 'Strikethrough', style: 'line-through' }
  ];

  const alignmentButtons = [
    { command: 'justifyLeft', icon: '⬅', title: 'Align Left' },
    { command: 'justifyCenter', icon: '⬌', title: 'Align Center' },
    { command: 'justifyRight', icon: '➡', title: 'Align Right' },
    { command: 'justifyFull', icon: '⬍', title: 'Justify' }
  ];

  const listButtons = [
    { command: 'insertUnorderedList', icon: '•', title: 'Bullet List' },
    { command: 'insertOrderedList', icon: '1.', title: 'Numbered List' }
  ];

  return (
    <div className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800 p-3">
        <div className="flex flex-wrap items-center gap-2">
          {/* Heading Selector */}
          <select
            onChange={(e) => execCommand('formatBlock', e.target.value)}
            className="text-xs border border-slate-300 dark:border-slate-600 rounded px-2 py-1 bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300"
          >
            <option value="div">Normal Text</option>
            <option value="h1">Heading 1</option>
            <option value="h2">Heading 2</option>
            <option value="h3">Heading 3</option>
            <option value="h4">Heading 4</option>
            <option value="h5">Heading 5</option>
          </select>

          <div className="w-px h-6 bg-slate-300 dark:bg-slate-600"></div>

          {/* Format Buttons */}
          {formatButtons.map((btn) => (
            <button
              key={btn.command}
              type="button"
              onClick={() => execCommand(btn.command)}
              className={`px-2 py-1 text-xs font-medium border border-slate-300 dark:border-slate-600 rounded hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors ${btn.style}`}
              title={btn.title}
            >
              {btn.icon}
            </button>
          ))}

          <div className="w-px h-6 bg-slate-300 dark:bg-slate-600"></div>

          {/* Alignment Buttons */}
          {alignmentButtons.map((btn) => (
            <button
              key={btn.command}
              type="button"
              onClick={() => execCommand(btn.command)}
              className="px-2 py-1 text-xs border border-slate-300 dark:border-slate-600 rounded hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors"
              title={btn.title}
            >
              {btn.icon}
            </button>
          ))}

          <div className="w-px h-6 bg-slate-300 dark:bg-slate-600"></div>

          {/* List Buttons */}
          {listButtons.map((btn) => (
            <button
              key={btn.command}
              type="button"
              onClick={() => execCommand(btn.command)}
              className="px-2 py-1 text-xs border border-slate-300 dark:border-slate-600 rounded hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors"
              title={btn.title}
            >
              {btn.icon}
            </button>
          ))}
        </div>
      </div>

      {/* Editor */}
      <textarea
        ref={editorRef}
        value={value.replace(/<[^>]*>/g, '')} // Strip HTML tags for textarea
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={`min-h-32 p-4 focus:outline-none text-slate-800 dark:text-slate-200 resize-none ${error ? 'border-red-500' : ''}`}
        style={{ minHeight: '150px' }}
        rows={6}
      />

      {/* Footer */}
      <div className="border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800 px-4 py-2 flex justify-between items-center">
        <div className="text-xs text-slate-500 dark:text-slate-400">
          {placeholder && !value && (
            <span className="absolute top-4 left-4 text-slate-400 dark:text-slate-500 pointer-events-none">
              {placeholder}
            </span>
          )}
        </div>
        <div className={`text-xs ${getWordCountColor(wordCount, maxWords)}`}>
          {wordCount.toLocaleString()} / {maxWords.toLocaleString()} words
        </div>
      </div>

      <style>{`
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
        }
        [contenteditable] h1 { font-size: 2em; font-weight: bold; margin: 0.67em 0; }
        [contenteditable] h2 { font-size: 1.5em; font-weight: bold; margin: 0.75em 0; }
        [contenteditable] h3 { font-size: 1.17em; font-weight: bold; margin: 0.83em 0; }
        [contenteditable] h4 { font-size: 1em; font-weight: bold; margin: 1.12em 0; }
        [contenteditable] h5 { font-size: 0.83em; font-weight: bold; margin: 1.5em 0; }
        [contenteditable] ul { list-style-type: disc; margin-left: 20px; }
        [contenteditable] ol { list-style-type: decimal; margin-left: 20px; }
        [contenteditable] li { margin: 0.5em 0; }
      `}</style>
    </div>
  );
}

RichTextEditor.propTypes = {
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  error: PropTypes.string,
  maxWords: PropTypes.number,
};

RichTextEditor.defaultProps = {
  placeholder: "",
  error: "",
  maxWords: 10000,
};

export default RichTextEditor;
