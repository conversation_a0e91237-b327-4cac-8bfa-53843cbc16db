import React from 'react';
import { NavLink } from 'react-router-dom';
import PropTypes from 'prop-types';

function SidebarNotificationLinks({ pathname }) {
  return (
    <li className={`px-3 py-2 rounded-lg mb-0.5 last:mb-0 transition-all duration-300 sidebar-link ${pathname.includes('notifications') && 'bg-gradient-to-r from-indigo-600 to-purple-600 shadow-lg'}`}>
      <NavLink
        end
        to="/notifications"
        className="block text-slate-200 truncate transition-all duration-300 hover:text-white"
      >
        <div className="flex items-center">
          <div className="shrink-0 h-6 w-6 flex items-center justify-center">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
              <path
                className={`${pathname.includes('notifications') ? 'text-white' : 'text-slate-400'} transition-colors duration-300`}
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-.61-.35-1.16-.78-1.65-1.27l1.65.95v.32zm2 0v-.32l1.65-.95c-.49.49-1.04.92-1.65 1.27zM18 14.24V9c0-3.07-1.64-5.64-4.5-6.32V2c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.64 3.36 6 5.93 6 9v5.24l-2 2V18h16v-1.76l-2-2z"
              />
            </svg>
          </div>
          <span className="text-sm font-medium ml-3 lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200 group-hover:translate-x-1 transition-transform">
            📢 Notifications
          </span>
        </div>
      </NavLink>
    </li>
  );
}

SidebarNotificationLinks.propTypes = {
  pathname: PropTypes.string.isRequired,
};

export default SidebarNotificationLinks;
