import React, { useState } from 'react';

function QualityMetricsCard() {
  const [selectedTest, setSelectedTest] = useState('all');

  const qualityTests = [
    { name: 'Fat Content', value: 3.8, target: 3.5, unit: '%', status: 'excellent', icon: '🧈' },
    { name: 'Protein', value: 3.2, target: 3.0, unit: '%', status: 'good', icon: '🥛' },
    { name: 'Lactose', value: 4.9, target: 4.8, unit: '%', status: 'excellent', icon: '🍼' },
    { name: 'pH Level', value: 6.7, target: 6.6, unit: '', status: 'good', icon: '⚗️' },
    { name: 'Bacteria Count', value: 15000, target: 20000, unit: 'CFU/ml', status: 'excellent', icon: '🦠' },
    { name: 'Temperature', value: 4.2, target: 4.0, unit: '°C', status: 'warning', icon: '🌡️' }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'excellent':
        return 'text-emerald-600 bg-emerald-100 dark:bg-emerald-900/30 dark:text-emerald-200';
      case 'good':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-200';
      case 'warning':
        return 'text-amber-600 bg-amber-100 dark:bg-amber-900/30 dark:text-amber-200';
      case 'critical':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-200';
      default:
        return 'text-slate-600 bg-slate-100 dark:bg-slate-700 dark:text-slate-400';
    }
  };

  const getProgressPercentage = (value, target, status) => {
    if (status === 'excellent') return 95;
    if (status === 'good') return 80;
    if (status === 'warning') return 60;
    return 40;
  };

  const filteredTests = selectedTest === 'all' 
    ? qualityTests 
    : qualityTests.filter(test => test.status === selectedTest);

  const getStatusScore = (status) => {
    switch (status) {
      case 'excellent': return 100;
      case 'good': return 85;
      case 'warning': return 70;
      default: return 50;
    }
  };

  const getStatusGradient = (status) => {
    switch (status) {
      case 'excellent': return 'bg-gradient-to-r from-emerald-500 to-teal-600';
      case 'good': return 'bg-gradient-to-r from-blue-500 to-indigo-600';
      case 'warning': return 'bg-gradient-to-r from-amber-500 to-orange-600';
      default: return 'bg-gradient-to-r from-red-500 to-pink-600';
    }
  };

  const overallScore = Math.round(
    qualityTests.reduce((acc, test) => {
      const score = getStatusScore(test.status);
      return acc + score;
    }, 0) / qualityTests.length
  );

  return (
    <div className="card glow-on-hover">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              Quality Metrics
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              Real-time quality testing results
            </p>
          </div>
          
          {/* Overall Score */}
          <div className="text-center">
            <div className="relative w-16 h-16">
              <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  className="text-slate-200 dark:text-slate-700"
                  stroke="currentColor"
                  strokeWidth="3"
                  fill="none"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  className="text-emerald-500"
                  stroke="currentColor"
                  strokeWidth="3"
                  strokeDasharray={`${overallScore}, 100`}
                  strokeLinecap="round"
                  fill="none"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-lg font-bold text-slate-800 dark:text-slate-100">
                  {overallScore}
                </span>
              </div>
            </div>
            <div className="text-xs text-slate-600 dark:text-slate-400 mt-1">
              Overall Score
            </div>
          </div>
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-2 mb-6">
          {['all', 'excellent', 'good', 'warning', 'critical'].map((filter) => (
            <button
              key={filter}
              onClick={() => setSelectedTest(filter)}
              className={`px-3 py-1 text-xs font-medium rounded-full transition-all duration-200 ${
                selectedTest === filter
                  ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white'
                  : 'bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-600'
              }`}
            >
              {filter.charAt(0).toUpperCase() + filter.slice(1)}
            </button>
          ))}
        </div>

        {/* Quality Tests */}
        <div className="space-y-4">
          {filteredTests.map((test, index) => (
            <div 
              key={test.name}
              className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 transition-all duration-200 group animate-fadeInUp"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl animate-pulse-gentle">
                  {test.icon}
                </div>
                <div>
                  <h4 className="font-medium text-slate-800 dark:text-slate-100">
                    {test.name}
                  </h4>
                  <div className="text-sm text-slate-600 dark:text-slate-400">
                    Target: {test.target}{test.unit}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                {/* Value */}
                <div className="text-right">
                  <div className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                    {test.value}{test.unit}
                  </div>
                  <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(test.status)}`}>
                    {test.status}
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="w-20">
                  <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-1000 ease-out ${getStatusGradient(test.status)}`}
                      style={{ 
                        width: `${getProgressPercentage(test.value, test.target, test.status)}%`,
                        animationDelay: `${index * 0.2}s`
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Summary Stats */}
        <div className="mt-6 pt-6 border-t border-slate-200 dark:border-slate-700">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-xl font-bold text-emerald-600">
                {qualityTests.filter(t => t.status === 'excellent').length}
              </div>
              <div className="text-xs text-slate-600 dark:text-slate-400">
                Excellent
              </div>
            </div>
            <div>
              <div className="text-xl font-bold text-blue-600">
                {qualityTests.filter(t => t.status === 'good').length}
              </div>
              <div className="text-xs text-slate-600 dark:text-slate-400">
                Good
              </div>
            </div>
            <div>
              <div className="text-xl font-bold text-amber-600">
                {qualityTests.filter(t => t.status === 'warning').length}
              </div>
              <div className="text-xs text-slate-600 dark:text-slate-400">
                Warning
              </div>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className="mt-6">
          <button className="w-full btn btn-secondary text-sm">
            View Full Quality Report
            <svg className="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}

export default QualityMetricsCard;
