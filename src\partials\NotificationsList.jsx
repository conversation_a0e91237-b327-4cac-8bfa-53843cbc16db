import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  getNotificationStatus,
  filterNotificationsByStatus,
  getStatusBadgeClass,
  getStatusDisplayText
} from '../utils/notificationUtils.js';

function NotificationsList({ searchTerm, typeFilter, userSegmentationFilter, countryFilter, statusFilter, dateRange }) {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState([]);
  const [filteredNotifications, setFilteredNotifications] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [sortField, setSortField] = useState('lastUpdated');
  const [sortOrder, setSortOrder] = useState('desc');

  // Mock data - TODO: Replace with actual API call // NOSONAR
  const mockNotifications = [
    {
      id: 1,
      title: 'New Dairy Analytics Features Available',
      description: 'We are excited to announce new analytics features in the Dairy Entelligen app that will help you track your farm performance more effectively...',
      type: 'marketing-campaigns',
      status: 'draft',
      lastUpdatedBy: 'Chantal M.',
      lastUpdated: '2025-06-25T10:33:00.000Z',
      userSegmentation: 'external',
      country: 'US',
      startDate: '2025-07-15T00:00:00.000Z',
      endDate: '2025-08-30T23:59:59.999Z'
    },
    {
      id: 2,
      title: 'System Maintenance Alert',
      description: 'Scheduled maintenance window for server upgrades and performance improvements. Users may experience brief interruptions during this time...',
      type: 'release-notes',
      status: 'published',
      lastUpdatedBy: 'Chantal M.',
      lastUpdated: '2025-06-24T15:22:00.000Z',
      userSegmentation: 'external',
      country: 'CA',
      startDate: '2025-07-01T00:00:00.000Z',
      endDate: '2025-08-15T23:59:59.999Z'
    },
    {
      id: 3,
      title: 'Holiday Schedule Changes',
      description: 'Please note the updated holiday schedule for farm operations during the winter season. All staff should review the new protocols...',
      type: 'special-action',
      status: 'published',
      lastUpdatedBy: 'Camilla E.',
      lastUpdated: '2024-12-24T09:15:00.000Z',
      userSegmentation: 'internal',
      country: 'US',
      startDate: '2024-12-01T00:00:00.000Z',
      endDate: '2025-01-15T23:59:59.999Z'
    },
    {
      id: 4,
      title: 'Bug Fixes and Security Updates',
      description: 'Critical security patches and bug fixes for improved app stability and data protection. Update recommended for all users...',
      type: 'release-notes',
      status: 'draft',
      lastUpdatedBy: 'Archita P.',
      lastUpdated: '2025-06-23T14:45:00.000Z',
      userSegmentation: 'internal',
      country: 'DE',
      startDate: '2025-07-10T00:00:00.000Z',
      endDate: '2025-08-10T23:59:59.999Z'
    },
    {
      id: 5,
      title: 'Summer Training Program',
      description: 'Annual summer training program for new dairy farm management techniques and best practices. Registration now open for all staff members...',
      type: 'marketing-campaigns',
      status: 'published',
      lastUpdatedBy: 'Mirena K.',
      lastUpdated: '2025-06-22T11:30:00.000Z',
      userSegmentation: 'internal',
      country: 'FR',
      startDate: '2025-08-01T00:00:00.000Z',
      endDate: '2025-09-30T23:59:59.999Z'
    },
    {
      id: 6,
      title: 'Holiday Promotion Campaign',
      description: 'Special holiday discounts on premium subscriptions and additional features. Limited time offer for new and existing users...',
      type: 'marketing-campaigns',
      status: 'published',
      lastUpdatedBy: 'Sarah K.',
      lastUpdated: '2024-12-21T16:20:00.000Z',
      userSegmentation: 'external',
      country: 'AU',
      startDate: '2024-12-25T00:00:00.000Z',
      endDate: '2025-01-10T23:59:59.999Z'
    }
  ];

  // TODO: /api/notifications - Fetch notifications from API // NOSONAR
  useEffect(() => {
    const fetchNotifications = async () => {
      setIsLoading(true);
      try {
        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setNotifications(mockNotifications);
      } catch (error) {
        console.error('Error fetching notifications:', error); // NOSONAR
      } finally {
        setIsLoading(false);
      }
    };

    fetchNotifications();
  }, []);

  // Filter notifications based on search term and filters
  useEffect(() => {
    let filtered = notifications;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(notification =>
        notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply type filter (multi-select)
    if (typeFilter && typeFilter.length > 0) {
      filtered = filtered.filter(notification => typeFilter.includes(notification.type));
    }

    // Apply user segmentation filter (multi-select)
    if (userSegmentationFilter && userSegmentationFilter.length > 0) {
      filtered = filtered.filter(notification => userSegmentationFilter.includes(notification.userSegmentation));
    }

    // Apply country filter (multi-select)
    if (countryFilter && countryFilter.length > 0) {
      filtered = filtered.filter(notification => countryFilter.includes(notification.country));
    }

    // Apply status filter (multi-select)
    filtered = filterNotificationsByStatus(filtered, statusFilter);

    // Apply date range filter
    if (dateRange && (dateRange.startDate || dateRange.endDate)) {
      filtered = filtered.filter(notification => {
        const notificationStart = new Date(notification.startDate);
        const notificationEnd = new Date(notification.endDate);

        let matchesDateRange = true;

        if (dateRange.startDate) {
          const filterStart = new Date(dateRange.startDate);
          matchesDateRange = matchesDateRange && (notificationEnd >= filterStart);
        }

        if (dateRange.endDate) {
          const filterEnd = new Date(dateRange.endDate);
          matchesDateRange = matchesDateRange && (notificationStart <= filterEnd);
        }

        return matchesDateRange;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      if (sortField === 'lastUpdated') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredNotifications(filtered);
  }, [notifications, searchTerm, typeFilter, userSegmentationFilter, countryFilter, statusFilter, dateRange, sortField, sortOrder]);

  const handleSort = (field) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const handleEdit = (notification) => {
    navigate(`/notifications/edit/${notification.id}`);
  };

  const handleDelete = async (notificationId) => {
    if (window.confirm('Are you sure you want to delete this notification?')) {
      try {
        // TODO: /api/notifications/{id} - Delete notification // NOSONAR
        console.log('Deleting notification:', notificationId); // NOSONAR
        
        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Remove from local state
        setNotifications(prev => prev.filter(n => n.id !== notificationId));
      } catch (error) {
        console.error('Error deleting notification:', error); // NOSONAR
      }
    }
  };

  const getStatusBadge = (notification) => {
    return getStatusBadgeClass(notification);
  };

  const getTypeDisplay = (type) => {
    switch (type) {
      case 'release-notes':
        return 'Release Notes';
      case 'special-actions':
        return 'Special Actions';
      case 'marketing-campaigns':
        return 'Marketing Campaigns';
      default:
        return type;
    }
  };

  const getSortIcon = (field) => {
    if (sortField !== field) {
      return (
        <svg className="w-4 h-4 ml-1 opacity-50" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      );
    }

    return sortOrder === 'asc' ? (
      <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
      </svg>
    ) : (
      <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
      </svg>
    );
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-slate-800 shadow-lg rounded-sm border border-slate-200 dark:border-slate-700">
        <div className="px-5 py-4">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <span className="ml-2 text-slate-600 dark:text-slate-400">Loading notifications...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="table-enhanced glow-on-hover">
      <header className="px-6 py-5 border-b border-slate-100 dark:border-slate-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              📋 Notifications
            </h2>
            <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
              Showing {filteredNotifications.length} of {notifications.length} notifications
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="badge badge-success">
              {notifications.filter(n => getNotificationStatus(n) === 'published').length} Published
            </div>
            <div className="badge badge-warning">
              {notifications.filter(n => getNotificationStatus(n) === 'draft').length} Drafts
            </div>
            <div className="badge badge-secondary">
              {notifications.filter(n => getNotificationStatus(n) === 'archived').length} Archived
            </div>
          </div>
        </div>
      </header>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="table-auto w-full">
          {/* Table header */}
          <thead className="text-xs font-semibold uppercase text-slate-400 dark:text-slate-500 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-700 dark:to-slate-800">
            <tr>
              <th
                className="p-4 whitespace-nowrap cursor-pointer hover:bg-slate-200 dark:hover:bg-slate-600 transition-all duration-200"
                onClick={() => handleSort('title')}
              >
                <div className="font-semibold text-left flex items-center">
                  📝 Title & Type
                  {getSortIcon('title')}
                </div>
              </th>
              <th
                className="p-4 whitespace-nowrap cursor-pointer hover:bg-slate-200 dark:hover:bg-slate-600 transition-all duration-200"
                onClick={() => handleSort('status')}
              >
                <div className="font-semibold text-left flex items-center">
                  📊 Status
                  {getSortIcon('status')}
                </div>
              </th>
              <th
                className="p-4 whitespace-nowrap cursor-pointer hover:bg-slate-200 dark:hover:bg-slate-600 transition-all duration-200"
                onClick={() => handleSort('lastUpdatedBy')}
              >
                <div className="font-semibold text-left flex items-center">
                  👤 Last Updated
                  {getSortIcon('lastUpdatedBy')}
                </div>
              </th>
              <th className="p-4 whitespace-nowrap">
                <div className="font-semibold text-left">⚡ Actions</div>
              </th>
            </tr>
          </thead>

          {/* Table body */}
          <tbody className="text-sm divide-y divide-slate-100 dark:divide-slate-700">
            {filteredNotifications.length === 0 ? (
              <tr>
                <td colSpan="4" className="p-8 text-center text-slate-500 dark:text-slate-400">
                  No notifications found matching your criteria.
                </td>
              </tr>
            ) : (
              filteredNotifications.map((notification, index) => (
                <tr
                  key={notification.id}
                  className="transition-all duration-200 ease-in-out hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 dark:hover:from-indigo-900/20 dark:hover:to-purple-900/20 animate-fadeInUp cursor-pointer"
                  style={{ animationDelay: `${index * 0.1}s` }}
                  onClick={() => handleEdit(notification)}
                >
                  <td className="p-4">
                    <div className="text-left">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                          {getTypeDisplay(notification.type).charAt(0)}
                        </div>
                        <div>
                          <div className="font-semibold text-slate-800 dark:text-slate-100">
                            {getTypeDisplay(notification.type)}
                          </div>
                          <div className="text-xs text-slate-500 dark:text-slate-400">
                            {notification.userSegmentation} • {notification.country}
                          </div>
                        </div>
                      </div>
                      <div className="text-slate-600 dark:text-slate-400 text-sm leading-relaxed">
                        {notification.description}
                      </div>
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="text-left">
                      <span className={getStatusBadge(notification)}>
                        {getStatusDisplayText(notification)}
                      </span>
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="text-left">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-gradient-to-r from-slate-400 to-slate-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                          {notification.lastUpdatedBy.charAt(0)}
                        </div>
                        <div>
                          <div className="text-sm font-medium text-slate-800 dark:text-slate-100">
                            {notification.lastUpdatedBy}
                          </div>
                          <div className="text-xs text-slate-500 dark:text-slate-400">
                            {new Date(notification.lastUpdated).toISOString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="p-4" onClick={(e) => e.stopPropagation()}>
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEdit(notification);
                        }}
                        className="p-2 text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 hover:bg-indigo-100 dark:hover:bg-indigo-900/30 rounded-lg transition-all duration-200"
                        title="Edit notification"
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                        </svg>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(notification.id);
                        }}
                        className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-all duration-200"
                        title="Delete notification"
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clipRule="evenodd" />
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L7.586 12l-1.293 1.293a1 1 0 101.414 1.414L9 13.414l1.293 1.293a1 1 0 001.414-1.414L10.414 12l1.293-1.293z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

NotificationsList.propTypes = {
  searchTerm: PropTypes.string,
  typeFilter: PropTypes.arrayOf(PropTypes.string),
  userSegmentationFilter: PropTypes.arrayOf(PropTypes.string),
  countryFilter: PropTypes.arrayOf(PropTypes.string),
  statusFilter: PropTypes.arrayOf(PropTypes.string),
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string,
  }),
};

NotificationsList.defaultProps = {
  searchTerm: '',
  typeFilter: [],
  userSegmentationFilter: [],
  countryFilter: [],
  statusFilter: [],
  dateRange: { startDate: '', endDate: '' },
};

export default NotificationsList;
