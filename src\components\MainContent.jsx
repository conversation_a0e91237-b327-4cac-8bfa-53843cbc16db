import {AuthenticatedTemplate, UnauthenticatedTemplate, useMsal} from '@azure/msal-react';
import {Route, Routes} from 'react-router-dom';
import {IdTokenData} from './DataDisplay.jsx';
import MainDashboard from '../pages/MainDashboard.jsx';
import UsersList from '../partials/UsersList.jsx';
import NotificationManagement from '../pages/NotificationManagement.jsx';
import NotificationSetup from '../pages/NotificationSetup.jsx';

function MainContent() {
  /**
   * useMsal is a hook that returns the PublicClientApplication instance.
   * https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-react/docs/hooks.md
   */
  const { instance } = useMsal();
  const activeAccount = instance.getActiveAccount();


  /**
   * Most applications will need to conditionally render certain components based on whether a user is signed in or not.
   * msal-react provides 2 easy ways to do this. AuthenticatedTemplate and UnauthenticatedTemplate components will
   * only render their children if a user is authenticated or unauthenticated, respectively. For more, visit:
   * https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-react/docs/getting-started.md
   */
  return (
    <div className="App">
      <AuthenticatedTemplate>
        <Routes>
          <Route exact path="/dash" element={<MainDashboard />} />
          {activeAccount ? (
            <>

              <Route exact path="/debug/token" element={<IdTokenData idTokenClaims={activeAccount.idTokenClaims} token={activeAccount.idToken} />} />
              <Route exact path="/community/users-tabs" element={<UsersList />} />
              <Route exact path="/notifications" element={<NotificationManagement />} />
              <Route exact path="/notifications/setup" element={<NotificationSetup />} />
              <Route exact path="/notifications/setup/:id" element={<NotificationSetup />} />
              <Route exact path="/notifications/edit/:id" element={<NotificationSetup />} />
              <Route exact path="/*" element={<MainDashboard />} />
            </>
          ): null}
        </Routes>
      </AuthenticatedTemplate>
      <UnauthenticatedTemplate>
        <h5 className="card-title">Please sign-in to see your profile information.</h5>
      </UnauthenticatedTemplate>
    </div>
  );
}

export default MainContent;
