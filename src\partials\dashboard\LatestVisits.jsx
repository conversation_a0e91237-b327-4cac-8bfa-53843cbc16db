import React from 'react';

import Image01 from '../../images/user-36-05.jpg';
import Image02 from '../../images/user-36-06.jpg';
import Image03 from '../../images/user-36-07.jpg';
import Image04 from '../../images/user-36-08.jpg';
import Image05 from '../../images/user-36-09.jpg';

function LatestVisits() {

  const visits = [
    {
      id: '0',
      image: Image01,
      name: '<PERSON>',
      email: 'alex<PERSON><PERSON>@gmail.com',
      visitName: 'Green Valley Farm Inspection',
      visitDate: '2024-01-15',
      toolsUsed: 5,
    },
    {
      id: '1',
      image: Image02,
      name: '<PERSON>',
      email: '<EMAIL>',
      visitName: 'Milk Quality Assessment',
      visitDate: '2024-01-14',
      toolsUsed: 3,
    },
    {
      id: '2',
      image: Image03,
      name: '<PERSON><PERSON>',
      email: 'mirkof<PERSON><PERSON>@gmail.com',
      visitName: 'Feed Analysis Review',
      visitDate: '2024-01-13',
      toolsUsed: 7,
    },
    {
      id: '3',
      image: Image04,
      name: '<PERSON>',
      email: '<EMAIL>',
      visitName: 'Profitability Consultation',
      visitDate: '2024-01-12',
      toolsUsed: 4,
    },
    {
      id: '4',
      image: Image05,
      name: 'Burak Long',
      email: '<EMAIL>',
      visitName: 'Cud Chewing Analysis',
      visitDate: '2024-01-11',
      toolsUsed: 2,
    },
  ];

  return (
    <div className="table-enhanced glow-on-hover">
      <header className="px-6 py-5 border-b border-slate-100 dark:border-slate-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              📋 Latest Visits
            </h2>
            <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
              Recent farm visits and tool usage
            </p>
          </div>
          <div className="badge badge-info">
            {visits.length} Recent Visits
          </div>
        </div>
      </header>
      <div className="p-4">

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="table-auto w-full">
            {/* Table header */}
            <thead className="text-xs font-semibold uppercase text-slate-400 dark:text-slate-500 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-700 dark:to-slate-800">
              <tr>
                <th className="p-4 whitespace-nowrap">
                  <div className="font-semibold text-left">👤 Name</div>
                </th>
                <th className="p-4 whitespace-nowrap">
                  <div className="font-semibold text-left">📧 Email</div>
                </th>
                <th className="p-4 whitespace-nowrap">
                  <div className="font-semibold text-left">🏭 Visit Name</div>
                </th>
                <th className="p-4 whitespace-nowrap">
                  <div className="font-semibold text-left">📅 Visit Date</div>
                </th>
                <th className="p-4 whitespace-nowrap">
                  <div className="font-semibold text-center">🔧 Tools Used</div>
                </th>
              </tr>
            </thead>
            {/* Table body */}
            <tbody className="text-sm divide-y divide-slate-100 dark:divide-slate-700">
              {
                visits.map((visit, index) => {
                  return (
                    <tr
                      key={visit.id}
                      className="transition-all duration-200 ease-in-out hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 dark:hover:from-indigo-900/20 dark:hover:to-purple-900/20 animate-fadeInUp"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <td className="p-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 shrink-0 mr-3">
                            <img className="rounded-full" src={visit.image} width="40" height="40" alt={visit.name} />
                          </div>
                          <div className="font-medium text-slate-800 dark:text-slate-100">{visit.name}</div>
                        </div>
                      </td>
                      <td className="p-4 whitespace-nowrap">
                        <div className="text-left text-slate-600 dark:text-slate-400">{visit.email}</div>
                      </td>
                      <td className="p-4 whitespace-nowrap">
                        <div className="text-left font-medium text-slate-800 dark:text-slate-100">{visit.visitName}</div>
                      </td>
                      <td className="p-4 whitespace-nowrap">
                        <div className="text-left text-slate-600 dark:text-slate-400">{visit.visitDate}</div>
                      </td>
                      <td className="p-4 whitespace-nowrap">
                        <div className="text-center">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-800 dark:from-indigo-900 dark:to-purple-900 dark:text-indigo-200">
                            {visit.toolsUsed} tools
                          </span>
                        </div>
                      </td>
                    </tr>
                  )
                })
              }
            </tbody>
          </table>

        </div>

      </div>
    </div>
  );
}

export default LatestVisits;
