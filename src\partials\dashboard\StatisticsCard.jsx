import React, { useState, useEffect } from 'react';

function StatisticsCard() {
  const [isLoading, setIsLoading] = useState(true);
  const [animatedValues, setAnimatedValues] = useState({
    accounts: 0,
    sites: 0,
    visits: 0
  });

  // Mock data - TODO: Replace with actual API calls // NOSONAR
  const targetValues = {
    accounts: 2847,
    sites: 156,
    visits: 18392
  };

  useEffect(() => {
    // Simulate loading
    const loadingTimer = setTimeout(() => {
      setIsLoading(false);
      
      // Animate numbers
      const duration = 2000; // 2 seconds
      const steps = 60; // 60 steps for smooth animation
      const stepDuration = duration / steps;
      
      let currentStep = 0;
      
      const animateNumbers = () => {
        currentStep++;
        const progress = currentStep / steps;
        const easeOutQuart = 1 - Math.pow(1 - progress, 4); // Easing function
        
        setAnimatedValues({
          accounts: Math.floor(targetValues.accounts * easeOutQuart),
          sites: Math.floor(targetValues.sites * easeOutQuart),
          visits: Math.floor(targetValues.visits * easeOutQuart)
        });
        
        if (currentStep < steps) {
          setTimeout(animateNumbers, stepDuration);
        }
      };
      
      animateNumbers();
    }, 1000);

    return () => clearTimeout(loadingTimer);
  }, []);

  const formatNumber = (num) => {
    return num.toLocaleString();
  };

  const statisticsData = [
    {
      title: 'Total DE App Accounts',
      value: animatedValues.accounts,
      change: '+12.5%',
      trend: 'up',
      icon: '👤',
      description: 'Registered users in the Dairy Enteligen app',
      color: 'from-blue-500 to-indigo-600'
    },
    {
      title: 'Total Sites',
      value: animatedValues.sites,
      change: '****%',
      trend: 'up',
      icon: '🏭',
      description: 'Active dairy farm sites monitored',
      color: 'from-emerald-500 to-teal-600'
    },
    {
      title: 'Total Visits',
      value: animatedValues.visits,
      change: '****%',
      trend: 'up',
      icon: '📈',
      description: 'App visits and user sessions',
      color: 'from-purple-500 to-pink-600'
    }
  ];

  if (isLoading) {
    return (
      <div className="card">
        <div className="p-6">
          <div className="animate-shimmer h-6 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={`stats-shimmer-${i}`} className="animate-shimmer h-32 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card glow-on-hover">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
              DE App Statistics
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              Overview of app usage and user engagement
            </p>
          </div>
          <div className="animate-float">
            <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" />
                <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Statistics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {statisticsData.map((stat, index) => (
            <div
              key={stat.title}
              className={`relative overflow-hidden rounded-xl bg-gradient-to-br ${stat.color} p-6 text-white group hover:scale-105 transition-all duration-300 animate-fadeInUp`}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute -right-4 -top-4 w-24 h-24 rounded-full bg-white"></div>
                <div className="absolute -left-2 -bottom-2 w-16 h-16 rounded-full bg-white"></div>
              </div>

              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <div className="text-3xl group-hover:scale-110 transition-transform duration-300">
                    {stat.icon}
                  </div>
                  <div className="text-right">
                    <div className="text-xs opacity-80 mb-1">
                      {stat.change}
                    </div>
                    <div className="flex items-center">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L10 4.414 6.707 7.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-xs opacity-80">vs last month</span>
                    </div>
                  </div>
                </div>

                <div className="mb-2">
                  <div className="text-3xl font-bold mb-1">
                    {formatNumber(stat.value)}
                  </div>
                  <div className="text-sm opacity-90">
                    {stat.title}
                  </div>
                </div>

                <div className="text-xs opacity-80 leading-relaxed">
                  {stat.description}
                </div>

                {/* Progress Bar */}
                <div className="mt-4">
                  <div className="w-full bg-white/20 rounded-full h-1.5 overflow-hidden">
                    <div 
                      className="h-1.5 bg-white rounded-full transition-all duration-2000 ease-out"
                      style={{ 
                        width: `${(stat.value / Math.max(...statisticsData.map(s => s.value))) * 100}%`,
                        animationDelay: `${index * 0.3}s`
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Summary Section */}
        <div className="mt-6 pt-6 border-t border-slate-200 dark:border-slate-700">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
              <div className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                94.8%
              </div>
              <div className="text-xs text-slate-600 dark:text-slate-400">
                User Satisfaction
              </div>
            </div>
            <div className="p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
              <div className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                24/7
              </div>
              <div className="text-xs text-slate-600 dark:text-slate-400">
                System Uptime
              </div>
            </div>
            <div className="p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
              <div className="text-lg font-semibold text-slate-800 dark:text-slate-100">
                15 min
              </div>
              <div className="text-xs text-slate-600 dark:text-slate-400">
                Avg Session Time
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default StatisticsCard;
